"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import Customers from "./customer-list";

interface CustomersPageViewProps {
  trans: {
    [key: string]: string;
  };
}

const CustomersPageView = ({ trans }: CustomersPageViewProps) => {
  return (
    <div className="p-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            {trans?.customers || "Clients"}
            {/* <Link href={"/customers/new"}>
              <Button>Add New Customer</Button>
            </Link> */}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Customers trans={trans} />
        </CardContent>
      </Card>
    </div>
  );
};

export default CustomersPageView;
