// User roles enum
export enum UserRoleType {
  Admin = "ROLE_ADMIN",
  Owner = "ROLE_OWNER",
  Manager = "ROLE_MANAGER",
  Staff = "ROLE_STAFF",
}

export interface RegisterUser {
  firstname: string;
  lastname: string;
  login: string;
  email: string;
  password: string;
}

//User Interface :
export interface IUser {
  id: number;
  login: string;
  firstName: string;
  lastName: string;
  email: string;
  imageUrl?: string | null;
  activated: boolean;
  createdAt: string;
  updatedAt: string;
}

//Branch Interface :
export interface IStore {
  id: number;
  name: string;
  address: string | null;
  phone: string | null;
  email: string | null;
  logoUrl: string | null;
  website: string | null;
}

//Branch Interface :
export interface IBranch {
  id: number;
  name: string;
  address?: string | null;
  phone?: string | null;
  email?: string | null;
  logoUrl?: string | null;
  store: IStore | null;
}

//Unit Interface :
export interface IUnit {
  id: number;
  name: string;
  abbreviation: string;
}

//NewUnintType :
export interface NewUnitType {
  name: string;
  abbreviation: string;
}

//Category Interface :
export interface ICategory {
  id: number;
  name: string;
  description?: string | null;
  imageUrl?: string | null;
  availabilityStatus: boolean;
  availableFrom?: string | null;
  availableTo?: string | null;
  branch: IBranch;
}

export interface NewCategoryType {
  name: string;
  description?: any | null;
  imageUrl?: string | null;
  availabilityStatus: boolean;
  availableFrom?: any | null;
  availableTo?: any | null;
  branchId: string;
}

//Modifier Interface :
export interface IOption {
  id: number;
  name: string;
  branch: IBranch;
}

export interface NewOptionType {
  name: string;
}

//Variation Interface :
export interface IVariation {
  id: number;
  name: string;
  description: string;
  availabilityStatus: boolean;
  imageUrl?: string | null;
  // branch: IBranch;
  option: IOption;
}

export interface NewVariationType {
  name: string;
  price: number;
  availabilityStatus: boolean;
  imageUrl?: string | null;
  branch: IBranch;
  modifier: IOption;
}

//Customer Interface :
export interface ICustomer {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  address: string;
  phone: string;
}

//Ingredient Employee :
export interface IEmployee {
  id: number;
  firstName: string;
  lastName: string;
  image?: string;
  email: string;
  address: string;
  phone: string;
  status: boolean;
  roles: string[];
}

//Coupon Interface
export interface ICoupon {
  id: number;
  code: string;
  description: string | null;
  discountType: string;
  discountValue: number;
  expiresAt: string;
  branch: IBranch;
}

export interface NewCouponType {
  code: string;
  description: string | null;
  discountType: string;
  discountValue: number;
  expiresAt: string;
  branch: IBranch;
}

//Discount Interface
export interface IDiscount {
  id: number;
  name: string;
  discountType: string;
  discountValue: number;
  startDate: string;
  endDate: string;
  store: IStore;
}

export interface NewDiscountType {
  name: string;
  discountType: string;
  discountValue: number;
  startDate: string;
  endDate: string;
  store: IStore;
}

//Tax Interface
export interface ITax {
  id: number;
  name: string;
  description?: string;
  taxRate: number;
  store: IStore;
}

//Product Interface
export interface IProduct {
  id: number;
  name?: string;
  description?: string | null;
  price: number;
  imageUrl?: string | null;
  availabilityStatus: boolean;
  availableFrom?: string | null;
  availableTo?: string | null;
  isAddon: boolean;
  isInventoryTracked: boolean;
  unit: IUnit;
  category: ICategory;
}

export interface NewProductType {
  name?: string;
  description?: string | null;
  price: number;
  imageUrl?: string | null;
  availabilityStatus: boolean;
  availableFrom?: string | null;
  availableTo?: string | null;
  isAddon: boolean;
  isInventoryTracked: boolean;
  categoryId: string;
  unitId: string;
}

export interface ILanguage {
  id: number;
  code: string;
  name: string;
}

// Enums for Order Type and Order Status
export enum OrderType {
  DINE_IN = "DINE_IN",
  TAKE_AWAY = "TAKE_AWAY",
  DELIVERY = "DELIVERY",
}

export enum OrderStatus {
  PENDING = "PENDING",
  PREPARING = "PREPARING",
  READY = "READY",
  COMPLETED = "COMPLETED",
  CANCELLED = "CANCELLED",
}

// Updated Order Interface
export interface IOrder {
  id: number;
  orderNumber: string;
  status: OrderStatus;
  type: OrderType;
  createdAt: string; // Date as string
  payment: IPayment;
  branch: IBranch;
  customer: ICustomer;
  coupon: ICoupon | null;
  kiosk: IKiosk | null;
}

// OrderDetails interface
export interface IOrderDetail {
  id: number;
  quantity: number;
  price: number;
  product: IProduct | null;
  ingredient: IVariation | null;
  order: IOrder;
}

// Payment Interface
export interface IPayment {
  id: number;
  amount: number | null;
  status: "PENDING" | "COMPLETED" | "FAILED" | null;
  createdAt: string | null;
  paymentMethod: "CASH" | "CREDIT_CARD" | "MOBILE_PAYMENT" | null;
}

// Enum for the status
export enum KioskStatus {
  IN_SERVICE = "IN_SERVICE",
  CLEANING = "CLEANING",
  DISABLED = "DISABLED",
}

// Interface for the kiosk table
export interface IKiosk {
  id: number;
  name: string;
  status: KioskStatus;
  branch_id: number | null; // branch_idIndex is optional and can be null
}
