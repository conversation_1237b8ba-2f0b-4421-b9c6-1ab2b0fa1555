"use client";

import { useEffect } from "react";

const OrderPrintPageView = ({ trans }: any) => {
  return <></>;
};

const ticketContent = `<div id="ticket" className="p-4 w-[500px]">
        <div className="flex items-center justify-center pt-3">
          <img src="https://stackfood-admin.6amtech.com/public/assets/admin/img/restaurant-invoice.png" className="h-12 w-12" alt="" />
        </div>
        <div className="mb-3 pt-3 text-center">
          <h5 className="text-lg font-bold">Hungry Puppets</h5>
          <h5 className="text-break font-medium">123 Main Street, Downtown, Cityville</h5>
          <h5 className="text-gray-500">11/Jan/2022 13:53</h5>
          <h5>
            <span>Téléphone</span> <span>:</span> <span>+****************</span>
          </h5>
        </div>
        <h5 className="mb-2 flex justify-between gap-2">
          <span>Type de Commande</span>
          <span>À emporter</span>
        </h5>
        <div className="rounded border border-dashed border-gray-400 p-3">
          <h5 className="flex justify-between gap-2">
            <span className="text-gray-500">ID de Commande</span>
            <span>100068</span>
          </h5>
          <h5 className="flex justify-between gap-2">
            <span className="text-gray-500">Nom du Client</span>
            <span>Non-Inscrit</span>
          </h5>
          <h5 className="flex justify-between gap-2">
            <span className="text-gray-500">Téléphone</span>
            <span>+****************</span>
          </h5>
        </div>
        <table className="mb-4 mt-4 w-full table-auto">
          <thead>
            <tr className="border-b border-dashed border-gray-400">
              <th className="text-left">QTE</th>
              <th className="text-left">Article</th>
              <th className="text-right">Prix</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>1x</td>
              <td>
                Medu Vada <br />
                <div className="text-sm text-gray-600">
                  <span>Prix : </span>
                  <span className="font-bold">€ 90.25</span>
                </div>
              </td>
              <td className="text-right">€ 90.25</td>
            </tr>
          </tbody>
        </table>
        <div className="mb-3 border-b border-dashed"></div>
        <div>
          <div className="px-3">
            <dl className="space-y-2 text-right">
              <div className="flex justify-between">
                <dt className="text-left text-gray-500">Prix des Articles</dt>
                <dd>€ 90.25</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-left text-gray-500">Coût des Suppléments</dt>
                <dd>€ 0.00</dd>
              </div>
              <div className="my-2 border-b border-dashed"></div>
              <div className="flex justify-between">
                <dt className="text-left font-medium">Sous-total</dt>
                <dd className="font-medium">€ 90.25</dd>
              </div>
              <div className="my-2 border-b border-dashed"></div>
              <div className="flex justify-between">
                <dt className="text-left text-gray-500">Remise</dt>
                <dd>- € 0.00</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-left text-gray-500">Remise Coupon</dt>
                <dd>- € 0.00</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-left text-gray-500">TVA</dt>
                <dd>€ 4.51</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-left text-gray-500">Pourboire livreur</dt>
                <dd>€ 0.00</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-left text-gray-500">Frais de livraison</dt>
                <dd>€ 0.00</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-left text-gray-500">Frais de service</dt>
                <dd>+ € 0.00</dd>
              </div>
              <div className="my-2 border-b border-dashed"></div>
              <div className="flex justify-between text-xl font-medium">
                <dt className="text-left">Total</dt>
                <dd>€ 94.76</dd>
              </div>
            </dl>
          </div>
        </div>
        <div className="my-2 border-b border-dashed"></div>
        <div className="flex justify-between">
          <span className="capitalize">Payé via : Paiement Ssl commerz</span>
        </div>
        <div className="my-2 border-b border-dashed"></div>
        <h5 className="mb-2 text-center font-medium">MERCI</h5>
        <div className="text-center">Pour avoir commandé sur QuickOrder</div>
        <div className="my-2 border-b border-dashed"></div>
        <span className="block text-center">&copy; 2024 QuickOrder. Tous droits réservés</span>
      </div>`;

export default OrderPrintPageView;
