import { api } from "@/config/axios.config";

export const uploadFile = async (file: File): Promise<any | null> => {
  try {
    const formData = new FormData();
    formData.append("file", file);

    const response = await api.post<any>("/user/upload", formData, {
      headers: {
        "content-type": "multipart/form-data",
      },
    });

    return response.data;
  } catch (error) {
    console.error("File upload error:", error);
    return null;
  }
};
