import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { getTables, updateTable } from "@/services/table";
import toast from "react-hot-toast";

export const getStatusColor = (status: string) => {
  switch (status) {
    case "AVAILABLE":
      return "bg-green-100 text-green-800 border-green-500";
    case "OCCUPIED":
      return "bg-blue-100 text-blue-800 border-blue-500";
    case "OUT_OF_SERVICE":
      return "bg-red-100 text-red-800 border-red-500";
    default:
      return "bg-gray-100 text-gray-800 border-gray-500";
  }
};

export const getStatusText = (status: string) => {
  switch (status) {
    case "AVAILABLE":
      return "Disponible";
    case "OCCUPIED":
      return "Réservé";
    case "OUT_OF_SERVICE":
      return "Hors service";
    default:
      return status;
  }
};

export const StatusDefinitions = () => {
  const statuses = [
    {
      name: "Disponible",
      color: "bg-green-500",
      description: "La table est disponible.",
    },
    {
      name: "Occupé",
      color: "bg-blue-500",
      description: "La table est réservée.",
    },
    {
      name: "Occupé",
      color: "bg-red-500",
      description: "La table est actuellement occupée.",
    },
  ];

  return (
    <div className="flex items-center space-x-4">
      {statuses.map((status) => (
        <div key={status.name} className="flex items-center space-x-2">
          <span className={`w-3 h-3 ${status.color} inline-block rounded-full`} />
          <span className="font-medium text-sm text-gray-700">{status.name}</span>
        </div>
      ))}
    </div>
  );
};

// export const RestaurantOverview = () => {
//   const totalTables = tables.length;
//   const availableTables = tables.filter((t) => t.status === "available").length;
//   const occupiedTables = tables.filter((t) => t.status === "occupied").length;
//   const reservedTables = tables.filter((t) => t.status === "reserved").length;

//   const stats = [
//     {
//       title: "Tables Totales",
//       value: totalTables,
//       className: "border-slate-200",
//     },
//     {
//       title: "Tables Disponibles",
//       value: availableTables,
//       className: "border-green-500",
//     },
//     {
//       title: "Tables Occupées",
//       value: occupiedTables,
//       className: "border-red-500",
//     },
//     {
//       title: "Tables Réservées",
//       value: reservedTables,
//       className: "border-blue-500",
//     },
//   ];

//   return (
//     <div>
//       <div className="grid grid-cols-4 gap-4">
//         {stats.map((stat) => (
//           <Card key={stat.title} className={`border-l-4 ${stat.className}`}>
//             <CardContent className="flex items-center justify-between p-2">
//               <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
//               <p className="text-2xl ml-4 font-bold">{stat.value}</p>
//             </CardContent>
//           </Card>
//         ))}
//       </div>
//     </div>
//   );
// };

const TableList = ({ trans }: any) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedTable, setSelectedTable] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [tablesList, setTablesList] = useState<any>([]);

  useEffect(() => {
    fetchTables();
  }, []);

  const fetchTables = async () => {
    setLoading(true);
    try {
      const tables = await getTables();
      if (tables) {
        setTablesList(tables);
      }
    } catch (error) {
      toast.error(trans?.tableFetchError || "Failed to fetch tables", { position: "bottom-center" });
    } finally {
      setLoading(false);
    }
  };

  const handleTableClick = (table: any) => {
    setSelectedTable(table);
    setIsOpen(true);
  };

  const handleStatusChange = async (newStatus: string) => {
    // Here you would implement the actual status update logic
    console.log(`Updating table ${selectedTable?.id} status to ${newStatus}`);
    try {
      await updateTable(selectedTable?.id, { status: newStatus });
      toast.success(trans?.tableUpdatedSuccess || "Table updated successfully", { position: "bottom-center" });

      fetchTables();
    } catch (error) {
      toast.error(trans?.tableSaveError || "Failed to save table", { position: "bottom-center" });
    }
    setIsOpen(false);
  };

  return (
    <>
      <div className="grid grid-cols-5 flex-wrap gap-6">
        {tablesList.map((table: any) => (
          <div
            key={table.id}
            onClick={() => handleTableClick(table)}
            className="h-48 bg-white border rounded-xl shadow-sm hover:shadow-md cursor-pointer"
          >
            <div className="p-4 h-full flex flex-col">
              <div className="flex justify-between items-start mb-4">
                <span className="font-bold text-2xl text-gray-800">{table.id}</span>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(table.status)}`}>{getStatusText(table.status)}</span>
              </div>

              <div className="flex-grow flex items-center justify-center">
                <div
                  className="w-20 h-20 bg-contain bg-center bg-no-repeat opacity-75"
                  style={{
                    backgroundImage: 'url("https://icon-library.com/images/dining-table-icon/dining-table-icon-13.jpg")',
                  }}
                />
              </div>

              {/* {table.code && (
                <div className="mt-4 text-sm text-gray-600">
                  Code: {table.code}
                </div>
              )} */}
            </div>
          </div>
        ))}
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Modifier le statut - Table {selectedTable?.id}</DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-3 gap-4 py-4">
            <Button color="success" onClick={() => handleStatusChange("AVAILABLE")} variant="outline" className="w-full h-20">
              Disponible
            </Button>
            <Button color="info" onClick={() => handleStatusChange("OCCUPIED")} variant="outline" className="w-full h-20">
              Réserver
            </Button>
            <Button color="destructive" onClick={() => handleStatusChange("OUT_OF_SERVICE")} variant="outline" className="w-full h-20">
              Occuper
            </Button>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOpen(false)} className="w-full">
              Annuler
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default TableList;
