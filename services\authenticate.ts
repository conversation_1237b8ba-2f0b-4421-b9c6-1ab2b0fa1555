import { api } from "@/config/axios.config";

interface AuthResponse {
  access_token: string;
}

interface AuthCredentials {
  email: string;
  password: string;
}

interface AuthPos {
  code: string;
}

export const authenticate = async (credentials: AuthCredentials): Promise<AuthResponse | null> => {
  try {
    const response = await api.post<AuthResponse>("/auth/login", credentials);
    return response.data;
  } catch (error) {
    console.error("Authentication error:", error);
    return null;
  }
};

export const authenticateByCode = async (body: AuthPos): Promise<AuthResponse | null> => {
  try {
    const response = await api.post<AuthResponse>("/auth/pos-login", { ...body, posId: process.env.NEXT_PUBLIC_POS_ID });
    return response.data;
  } catch (error) {
    console.error("Authentication error:", error);
    return null;
  }
};
