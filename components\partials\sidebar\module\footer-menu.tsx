"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { updatePosSession } from "@/services/pos-session";
import { useAuthStore } from "@/store";
import { Icon } from "@iconify/react";
import { signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useState } from "react";

const FooterMenu = () => {
  const router = useRouter();
  const { posSession, setUserSession, setPosSession } = useAuthStore();
  const [isConfirmationPopupOpen, setConfirmationPopupOpen] = useState(false);

  const handlePauseSession = async () => {
    const sessionBody = {
      closedAt: new Date(),
      status: "CLOSED",
    };

    const openPosSession = await updatePosSession(posSession.id, sessionBody);

    setPosSession({ ...openPosSession, redirectedFrom: "pos" });

    router.push("/session");
  };

  const handleLogout = async () => {
    const sessionBody = {
      closedAt: new Date(),
      status: "CLOSED",
    };

    await updatePosSession(posSession.id, sessionBody);

    setPosSession(null);
    setUserSession(null);
    signOut();
  };

  return (
    <div className="space-y-2 flex flex-col items-center justify-center pb-4">
      {/* <ProfileInfo /> */}

      <div
        onClick={() => handlePauseSession()}
        className={cn(
          "w-full  text-center border-[1px] text-black dark:text-default-400 hover:bg-primary/10  hover:text-primary  p-2 mx-auto rounded-md  transition-all duration-300 flex flex-col items-center justify-center cursor-pointer relative"
        )}
      >
        <Icon icon="heroicons:pause" className=" h-6 w-6" />
        <span className="text-xs mt-2">Pause Session</span>
      </div>
      <div
        onClick={handleLogout}
        className={cn(
          "w-full text-center border-[1px] text-black dark:text-default-400 hover:bg-primary/10  hover:text-primary  p-2 mx-auto rounded-md  transition-all duration-300 flex flex-col items-center justify-center cursor-pointer relative"
        )}
      >
        <Icon icon="heroicons:power" className=" h-6 w-6" />
        <span className="text-xs mt-2">Déconnexion</span>
      </div>

      <AlertDialog open={isConfirmationPopupOpen} onOpenChange={() => setConfirmationPopupOpen(!isConfirmationPopupOpen)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmer la Déconnexion</AlertDialogTitle>
            <AlertDialogDescription>
              Êtes-vous sûr de vouloir deconnecter ? Cette action vous déconnectera de votre session actuelle.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel asChild>
              <Button variant="outline">Annuler</Button>
            </AlertDialogCancel>
            <AlertDialogAction asChild>
              <Button onClick={() => handleLogout()}>Confirmer la Déconnexion</Button>
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
export default FooterMenu;
