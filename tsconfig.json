{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "target": "esnext", "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "noUnusedParameters": false, "preserveWatchOutput": true, "strictNullChecks": false, "composite": false, "declaration": true, "declarationMap": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "noUnusedLocals": false, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"]}, "skipDefaultLibCheck": true, "allowSyntheticDefaultImports": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "lib/utils.js", "components/ui/switch.jsx", "components/ui/input.tsx", "components/dasboard-select.jsx", "components/ui/tabs.jsx", "components/ui/slider.jsx", "app/[lang]/not-found.js", ".next/types/**/*.ts"], "exclude": ["node_modules"]}