import { api } from "@/config/axios.config";

// Function to get the list of Coupons
export const getCoupons = async (branchId: number): Promise<any[] | null> => {
  try {
    const response = await api.get<any[]>(`/coupons/branch/${branchId}`);
    return response.data;
  } catch (error) {
    console.error("Get Coupons error:", error);
    return null;
  }
};

// Function to get the list of Coupons
interface anyResponse {
  totalCount: number;
  data: any[];
}

export const getCouponsWithPagination = async (
  page: number = 0,
  size: number = 10,
  sortBy: string = "name",
  sortDirection: "asc" | "desc" = "asc"
): Promise<anyResponse | null> => {
  try {
    const response = await api.get<any[]>(`/coupons?size=${size}&page=${page}&sort=${sortBy},${sortDirection}`);

    // Extract total count from headers and the data from the response
    const totalCount = parseInt(response.headers["x-total-count"]);
    const data: any[] = response.data;

    return { totalCount, data };
  } catch (error) {
    console.error("Get Coupons error:", error);
    return null;
  }
};

// Function to get the Coupon by Id
export const getCouponById = async (CouponId: number, branchId: number): Promise<any | null> => {
  try {
    const response = await api.get<any>(`/coupons/branch/${branchId}/${CouponId}`);
    return response.data;
  } catch (error) {
    console.error("Get Coupons error:", error);
    return null;
  }
};

// Function to get the Coupon by code
export const getCouponByCode = async (CouponCode: string): Promise<any | null> => {
  try {
    const response = await api.get<any>(`/coupons/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/code/${CouponCode}`);
    return response.data;
  } catch (error) {
    console.error("Get Coupons error:", error);
    return null;
  }
};

// Function to create a new Coupon
export const createCoupon = async (newCoupon: any, branchId: number): Promise<any | null> => {
  try {
    const response = await api.post<any>(`/coupons/branch/${branchId}`, { ...newCoupon, branchId: branchId.toString() });
    return response.data;
  } catch (error) {
    console.error("Create Coupon error:", error);
    return null;
  }
};

// Function to delete a Coupon by ID
export const deleteCoupon = async (CouponId: number, branchId: number): Promise<boolean> => {
  try {
    await api.delete(`/coupons/branch/${branchId}/${CouponId}`);
    return true;
  } catch (error) {
    console.error("Delete Coupon error:", error);
    return false;
  }
};

// Function to update an existing Coupon by ID
export const updateCoupon = async (CouponId: number, updatedCoupon: any, branchId: number): Promise<any | null> => {
  try {
    const response = await api.patch<any>(`/coupons/branch/${branchId}/${CouponId}`, { ...updatedCoupon, branchId: branchId.toString() });
    return response.data;
  } catch (error) {
    console.error("Update Coupon error:", error);
    return null;
  }
};
