import { TableHead } from "./ui/table";

interface SortableTableHeadProps {
  label: string;
  sortKey: string;
  sortColumn: string;
  sortDirection: string;
  onSort: (column: string) => void;
  trans: any;
  className?: string;
}

const SortableTableHead = ({
  label,
  sortKey,
  sortColumn,
  sortDirection,
  onSort,
  trans,
  className,
}: SortableTableHeadProps) => {
  const isSorted = sortColumn === sortKey;
  return (
    <TableHead
      className={`${className} font-semibold`}
      //  ${
      //   isSorted ? "text-primary-400" : "hover:text-primary-400"
      // }
      //onClick={() => onSort(sortKey)}
    >
      {trans?.[label]}
      {/* {isSorted && (sortDirection === "asc" ? " ↑" : " ↓")} */}
    </TableHead>
  );
};

export default SortableTableHead;
