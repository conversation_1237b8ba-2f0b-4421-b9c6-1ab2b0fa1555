import { useState } from "react";
import { But<PERSON> } from "../ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "../ui/command";
import { Icon } from "@iconify/react";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";

export const SelectCustomer = ({ customers, selectedCustomer, setSelectedCustomer }: any) => {
  const [open, setOpen] = useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          color="secondary"
          role="combobox"
          aria-expanded={open}
          className="hover:text-default-300 hover:bg-transparent hover:border-default-300 border-default-300 font-normal w-full justify-between border px-3 py-2"
        >
          <Icon icon="heroicons:user" className="h-4 w-4" />
          {selectedCustomer ? <span>{`${selectedCustomer.firstName} ${selectedCustomer.lastName}`}</span> : "Client Non-Inscrit"}
          <ChevronsUpDown className="mx-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0 z-[9999]">
        <Command className="w-full">
          <CommandInput placeholder="Rechercher un client" />
          <CommandEmpty>Aucun client trouvé.</CommandEmpty>
          <CommandGroup className="max-h-[300px] overflow-y-auto">
            <CommandItem
              key={0}
              onSelect={() => {
                setSelectedCustomer(null);
                setOpen(false);
              }}
            >
              <Check className={cn("mr-2 h-4 w-4", selectedCustomer === null ? "opacity-100" : "opacity-0")} />
              Client Non-Inscrit
            </CommandItem>
            {customers.map((customer: any) => (
              <CommandItem
                key={customer.id}
                onSelect={() => {
                  setSelectedCustomer(customer);
                  setOpen(false);
                }}
              >
                <Check className={cn("mr-2 h-4 w-4", selectedCustomer?.id === customer.id ? "opacity-100" : "opacity-0")} />
                {`${customer.firstName} ${customer.lastName}`}
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
