import { api } from "@/config/axios.config";
import { IBranch, IStore } from "@/lib/interface";

export const getAccount = async (token: any): Promise<any | null> => {
  try {
    const response = await api.get<any>("auth/me", {
      headers: { Authorization: `Bearer ${token}` },
    });
    return response.data;
  } catch (error) {
    console.error("Get account error:", error);
    return null;
  }
};

// Function to get the list of stores by user ID
export const getStoresByUserId = async (
  userId: string
): Promise<IStore[] | null> => {
  try {
    const response = await api.get<IStore[]>(`/user/${userId}/stores`);
    return response.data;
  } catch (error) {
    console.error(`Get Stores by User ID error for user ${userId}:`, error);
    return null;
  }
};

// Function to get the list of branches by user ID
export const getBranchesByUserId = async (
  userId: string
): Promise<IBranch[] | null> => {
  try {
    const response = await api.get<IBranch[]>(`/user/${userId}/branches`);
    return response.data;
  } catch (error) {
    console.error(`Get Branches by User ID error for user ${userId}:`, error);
    return null;
  }
};
