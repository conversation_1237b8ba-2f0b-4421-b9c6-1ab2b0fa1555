"use client";
import Image from "next/image";

import auth3Light from "@/public/images/auth/auth3-light.png";
import auth3Dark from "@/public/images/auth/auth3-dark.png";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useMediaQuery } from "@/hooks/use-media-query";

const VerifyEmailPage = () => {
  const isDesktop2xl = useMediaQuery("(max-width: 1530px)");
  return (
    <div className="loginwrapper  flex justify-center items-center relative overflow-hidden">
      <Image
        src={auth3Dark}
        alt="background image"
        className="absolute top-0 left-0 w-full h-full light:hidden"
      />
      <Image
        src={auth3Light}
        alt="background image"
        className="absolute top-0 left-0 w-full h-full dark:hidden"
      />
      <div className="w-full bg-card   max-w-xl  rounded-xl relative z-10 2xl:p-16 xl:p-12 p-10 m-4">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-primary">QuickOrder</h1>

          <div className="2xl:mt-8 mt-6 2xl:text-3xl text-2xl font-bold text-default-900">
            Verify your email address
          </div>
          <div className="2xl:text-lg text-base text-default-600 mt-2 leading-6">
            To get started with QuickOrder, we simply need <br /> to verify your
            email address.
          </div>
        </div>
        <Button className="w-full mt-6" size={!isDesktop2xl ? "lg" : "md"}>
          Click to Verify
        </Button>
      </div>
    </div>
  );
};

export default VerifyEmailPage;
