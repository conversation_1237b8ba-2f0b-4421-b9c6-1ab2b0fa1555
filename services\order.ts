import { api } from "@/config/axios.config";
import { IOrder, IOrderDetail } from "@/lib/interface";

// Function to get the list of orders
export const getOrders = async (onlyToday: boolean = false): Promise<IOrder[] | null> => {
  try {
    const response = await api.get<IOrder[]>(`/orders/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}?onlyToday=${onlyToday}`);
    return response.data;
  } catch (error) {
    console.error("Get Orders error:", error);
    return null;
  }
};

// Function to get the list of orders with pagination
interface IOrderResponse {
  totalCount: number;
  data: IOrder[];
}

export const getOrdersWithPagination = async (
  page: number = 0,
  size: number = 10,
  sortBy: string = "name",
  sortDirection: "asc" | "desc" = "asc"
): Promise<IOrderResponse | null> => {
  try {
    const response = await api.get<IOrder[]>(`/orders?size=${size}&page=${page}&sort=${sortBy},${sortDirection}`);

    // Extract total count from headers and the data from the response
    const totalCount = parseInt(response.headers["x-total-count"]);
    const data: IOrder[] = response.data;

    return { totalCount, data };
  } catch (error) {
    console.error("Get Orders error:", error);
    return null;
  }
};

// Function to get the order by Id
export const getOrderById = async (orderId: number): Promise<IOrder | null> => {
  try {
    const response = await api.get<IOrder>(`/orders/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/${orderId}`);
    return response.data;
  } catch (error) {
    console.error("Get Order by ID error:", error);
    return null;
  }
};

// Function to get the order by Id
export const getOrderByOrderNumber = async (orderNumber: string, onlyToday: boolean = false): Promise<IOrder | null> => {
  try {
    const response = await api.get<IOrder>(
      `/orders/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/by-order-number/${orderNumber}?onlyToday=${onlyToday}`
    );
    return response.data;
  } catch (error) {
    console.error("Get Order by ID error:", error);
    return null;
  }
};

export const getOrderDetailsByOrderId = async (orderId: number): Promise<IOrderDetail[] | null> => {
  try {
    // Making an API request to get all order details
    const response = await api.get<IOrderDetail[]>(`/order-details/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}?size=1000`);

    // Filtering order details that have the matching order.id
    const filteredOrderDetails = response.data.filter((orderDetail) => orderDetail.order.id === orderId);

    return filteredOrderDetails;
  } catch (error) {
    console.error(`Get Order Details error for order ${orderId}:`, error);
    return null;
  }
};

export const getStats = async (onlyToday: boolean = false): Promise<any> => {
  try {
    const response = await api.get<any>(`/orders/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/status/stats?onlyToday=${onlyToday}`);
    return response.data;
  } catch (error) {
    console.error("Get Stats error:", error);
    return null;
  }
};

export const getOrdersByStatus = async (status: string, onlyToday: boolean = false): Promise<any> => {
  try {
    const response = await api.get<IOrder[]>(`/orders/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/by-status/${status}?onlyToday=${onlyToday}`);
    return response.data;
  } catch (error) {
    console.error("Get Orders by Status error:", error);
    return null;
  }
};
// Function to create a new order
export const getOrderWithDetails = async (orderId: number): Promise<any> => {
  try {
    const response = await api.get<any>(`/orders/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/with-details/${orderId}`);
    return response.data;
  } catch (error) {
    console.error("Get Order with Details error:", error);
    return null;
  }
};

// // Function to create a new order
export const createOrder = async (newOrder: any): Promise<IOrder | null> => {
  try {
    const response = await api.post<IOrder>(`/orders/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}`, newOrder);
    return response.data;
  } catch (error) {
    console.error("Create Order error:", error);
    return null;
  }
};

// Function to create a new order
export const createOrderWithDetails = async (body: any): Promise<any | null> => {
  try {
    const response = await api.post<IOrder>(`/orders/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/with-details`, body);
    return response.data;
  } catch (error) {
    console.error("Create Order error:", error);
    return null;
  }
};

// Function to update an existing order by ID
export const updateOrder = async (orderId: number, updatedOrder: Partial<IOrder>): Promise<IOrder | null> => {
  try {
    const response = await api.put<IOrder>(`/orders/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/${orderId}`, updatedOrder);
    return response.data;
  } catch (error) {
    console.error("Update Order error:", error);
    return null;
  }
};

// Function to update order status
export const updateOrderStatus = async (orderId: string, newStatus: string): Promise<any | null> => {
  try {
    const updateData = { status: newStatus };
    const response = await api.patch<any>(`/orders/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/${orderId}`, updateData);
    return response.data;
  } catch (error) {
    console.error("Update Order Status error:", error);
    return null;
  }
};

// Function to generate order ticket :
export const generateOrderTicket = async (orderId: number): Promise<string | null> => {
  try {
    const response = await api.get<string>(`/orders/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/${orderId}/print-ticket`);
    return response.data;
  } catch (error) {
    console.error("Generate Order Ticket error:", error);
    return null;
  }
};

// // Function to delete an order by ID
// export const deleteOrder = async (orderId: number): Promise<boolean> => {
//   try {
//     await api.delete(`/orders/${orderId}`);
//     return true;
//   } catch (error) {
//     console.error("Delete Order error:", error);
//     return false;
//   }
// };

// // Function to update an existing order by ID
// export const updateOrder = async (
//   orderId: number,
//   updatedOrder: Partial<IOrder>
// ): Promise<IOrder | null> => {
//   try {
//     const response = await api.put<IOrder>(`/orders/${orderId}`, updatedOrder);
//     return response.data;
//   } catch (error) {
//     console.error("Update Order error:", error);
//     return null;
//   }
// };
