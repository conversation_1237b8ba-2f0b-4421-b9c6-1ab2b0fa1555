import { api } from "@/config/axios.config";
import { IOrder, IOrderDetail } from "@/lib/interface";

// Function to get the list of order
export const getOrders = async (onlyToday: boolean = false): Promise<IOrder[] | null> => {
  try {
    const response = await api.get<IOrder[]>(`/order?onlyToday=${onlyToday}`);
    return response.data;
  } catch (error) {
    console.error("Get Orders error:", error);
    return null;
  }
};

// Function to get the list of orders with filters
export const getOrdersFiltred = async (filters?: {
  status?: string[];
  type?: string[];
  startDate?: string;
  endDate?: string;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDirection?: "asc" | "desc";
}): Promise<any> => {
  try {
    const params = new URLSearchParams();

    if (filters?.status) filters.status.forEach((s) => params.append("status", s));
    if (filters?.type) filters.type.forEach((t) => params.append("type", t));
    if (filters?.startDate) params.append("startDate", filters.startDate);
    if (filters?.endDate) params.append("endDate", filters.endDate);
    if (filters?.limit) params.append("limit", filters.limit.toString());
    if (filters?.offset) params.append("offset", filters.offset.toString());
    if (filters?.sortBy) params.append("sortBy", filters.sortBy);
    if (filters?.sortDirection) params.append("sortDirection", filters.sortDirection);

    const response = await api.get<{ data: IOrder[]; totalCount: number }>(
      `/order/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/filter?${params.toString()}`
    );
    return response.data;
  } catch (error) {
    console.error("Get Orders error:", error);
    return [];
  }
};

// Function to get the list of order with pagination
interface IOrderResponse {
  totalCount: number;
  data: IOrder[];
}

export const getOrdersWithPagination = async (
  page: number = 0,
  size: number = 10,
  sortBy: string = "name",
  sortDirection: "asc" | "desc" = "asc"
): Promise<IOrderResponse | null> => {
  try {
    const response = await api.get<IOrder[]>(`/order?size=${size}&page=${page}&sort=${sortBy},${sortDirection}`);

    // Extract total count from headers and the data from the response
    const totalCount = parseInt(response.headers["x-total-count"]);
    const data: IOrder[] = response.data;

    return { totalCount, data };
  } catch (error) {
    console.error("Get Orders error:", error);
    return null;
  }
};

// Function to get the order by Id
export const getOrderById = async (orderId: number): Promise<IOrder | null> => {
  try {
    const response = await api.get<IOrder>(`/order/${orderId}`);
    return response.data;
  } catch (error) {
    console.error("Get Order by ID error:", error);
    return null;
  }
};

// Function to get the order by Id
export const getOrderByOrderNumber = async (orderNumber: string, onlyToday: boolean = false): Promise<IOrder | null> => {
  try {
    const response = await api.get<IOrder>(`/order/by-order-number/${orderNumber}?onlyToday=${onlyToday}`);
    return response.data;
  } catch (error) {
    console.error("Get Order by ID error:", error);
    return null;
  }
};

export const getOrderDetailsByOrderId = async (orderId: number): Promise<IOrderDetail[] | null> => {
  try {
    // Making an API request to get all order details
    const response = await api.get<IOrderDetail[]>(`/order-details?size=1000`);

    // Filtering order details that have the matching order.id
    const filteredOrderDetails = response.data.filter((orderDetail) => orderDetail.order.id === orderId);

    return filteredOrderDetails;
  } catch (error) {
    console.error(`Get Order Details error for order ${orderId}:`, error);
    return null;
  }
};

export const getStats = async (startDate: string, endDate: string): Promise<any> => {
  try {
    const response = await api.get(`/order/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/filter?startDate=${startDate}&endDate=${endDate}`);
    if (response.data) {
      const stats = response.data.reduce(
        (acc, order) => {
          acc[order.status.toLowerCase()]++;
          return acc;
        },
        { all: 0, pending: 0, preparing: 0, ready: 0, completed: 0, cancelled: 0 }
      );

      return { ...stats, all: response.data.length };
    }

    return { all: 0, pending: 0, preparing: 0, ready: 0, completed: 0, cancelled: 0 };
  } catch (error) {
    console.error("Get Stats error:", error);
    return { all: 0, pending: 0, preparing: 0, ready: 0, completed: 0, cancelled: 0 };
  }
};

export const getOrdersByStatus = async (status: string, onlyToday: boolean = false): Promise<any> => {
  try {
    const response = await api.get<IOrder[]>(`/order/by-status/${status}?onlyToday=${onlyToday}`);
    return response.data;
  } catch (error) {
    console.error("Get Orders by Status error:", error);
    return null;
  }
};
// Function to create a new order
export const getOrderWithDetails = async (orderId: number): Promise<any> => {
  try {
    const response = await api.get<any>(`/order/branch/1/${orderId}`);
    return response.data;
  } catch (error) {
    console.error("Get Order with Details error:", error);
    return null;
  }
};

// // Function to create a new order
export const createOrder = async (newOrder: any): Promise<IOrder | null> => {
  try {
    const response = await api.post<IOrder>(`/order`, newOrder);
    return response.data;
  } catch (error) {
    console.error("Create Order error:", error);
    return null;
  }
};

// Function to create a new order
export const createOrderWithDetails = async (body: any): Promise<any | null> => {
  try {
    const response = await api.post<IOrder>(`/order/with-details`, body);
    return response.data;
  } catch (error) {
    console.error("Create Order error:", error);
    return null;
  }
};

// Function to update an existing order by ID
export const updateOrder = async (orderId: number, updatedOrder: Partial<IOrder>): Promise<IOrder | null> => {
  try {
    const response = await api.put<IOrder>(`/order/${orderId}`, updatedOrder);
    return response.data;
  } catch (error) {
    console.error("Update Order error:", error);
    return null;
  }
};

// Function to update order status
export const updateOrderStatus = async (orderId: string, newStatus: string): Promise<any | null> => {
  try {
    const updateData = { status: newStatus };
    const response = await api.patch<any>(`/order/${orderId}/status`, updateData);
    return response.data;
  } catch (error) {
    console.error("Update Order Status error:", error);
    return null;
  }
};

// Function to generate order ticket :
export const generateOrderTicket = async (orderId: number): Promise<string | null> => {
  try {
    const response = await api.get<string>(`/order/${orderId}/print-ticket`);
    return response.data;
  } catch (error) {
    console.error("Generate Order Ticket error:", error);
    return null;
  }
};

// Calculate real taxes for cart items using your existing tax system
export const calculateCartTaxes = async (basketItems: any) => {
  try {
    const cartBody = {
      branchId: process.env.NEXT_PUBLIC_BRANCH_ID,
      items: basketItems.map((item: any) => ({
        productId: item.product.id.toString(),
        originalPrice: parseFloat(item.product.price) / item.qty,
        quantity: item.qty,
        variations: item.variations.map((variationGroup: any) => ({
          variationPrice: variationGroup.variations[0].price,
        })),
      })),
    };

    const response = await api.post<string>(`/tax/calculate/cart`, cartBody);
    console.log("Cart Data : ", response.data);
    return response.data;
  } catch (error) {
    console.error("Calculate Cart Tax error:", error);
    return null;
  }
};

// // Function to delete an order by ID
// export const deleteOrder = async (orderId: number): Promise<boolean> => {
//   try {
//     await api.delete(`/order/${orderId}`);
//     return true;
//   } catch (error) {
//     console.error("Delete Order error:", error);
//     return false;
//   }
// };

// // Function to update an existing order by ID
// export const updateOrder = async (
//   orderId: number,
//   updatedOrder: Partial<IOrder>
// ): Promise<IOrder | null> => {
//   try {
//     const response = await api.put<IOrder>(`/order/${orderId}`, updatedOrder);
//     return response.data;
//   } catch (error) {
//     console.error("Update Order error:", error);
//     return null;
//   }
// };
