import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import Orders from "./order-list";

const OrderPageView = ({ trans }: any) => {
  return (
    <div className="p-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">{trans?.orders}</CardTitle>
        </CardHeader>
        <CardContent>
          <Orders trans={trans} />
        </CardContent>
      </Card>
    </div>
  );
};

export default OrderPageView;
