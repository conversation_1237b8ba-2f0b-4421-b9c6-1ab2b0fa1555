import { api } from "@/config/axios.config";

// Function to get the list of all payment-method
export const getPaymentMethods = async (): Promise<any[] | null> => {
  try {
    const response = await api.get<any[]>("/payment-method");
    return response.data;
  } catch (error) {
    console.error("Get payment-method error:", error);
    return null;
  }
};

// Function to get a paymentMethod by ID
export const getPaymentMethodById = async (paymentMethodId: number): Promise<any | null> => {
  try {
    const response = await api.get<any>(`/payment-method/${paymentMethodId}`);
    return response.data;
  } catch (error) {
    console.error(`Get paymentMethod by ID error: ${error}`);
    return null;
  }
};

// Function to create a new paymentMethod
export const createPaymentMethod = async (newPaymentMethod: any): Promise<any | null> => {
  try {
    const response = await api.post<any>("/payment-method", newPaymentMethod);
    console.log(response);
    return response.data;
  } catch (error) {
    console.error("Create paymentMethod error:", error);
    return null;
  }
};

// Function to update an existing paymentMethod by ID
export const updatePaymentMethod = async (paymentMethodId: number, updatedPaymentMethod: Partial<any>): Promise<any | null> => {
  try {
    const response = await api.patch<any>(`/payment-method/${paymentMethodId}`, updatedPaymentMethod);
    return response.data;
  } catch (error) {
    console.error("Update paymentMethod error:", error);
    return null;
  }
};

// Function to delete a paymentMethod by ID
export const deletePaymentMethod = async (paymentMethodId: number): Promise<boolean> => {
  try {
    await api.delete(`/payment-method/${paymentMethodId}`);
    return true;
  } catch (error) {
    console.error("Delete paymentMethod error:", error);
    return false;
  }
};
