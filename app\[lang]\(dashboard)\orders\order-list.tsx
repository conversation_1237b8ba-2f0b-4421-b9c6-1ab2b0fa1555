"use client";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react";

import { Badge } from "@/components/ui/badge";

import NoResult from "@/components/no-result";
import SortableTableHead from "@/components/sortable-table-head";
import TablePagination from "@/components/table-pagination";
import { Input } from "@/components/ui/input";
import { IOrder, OrderStatus, OrderType } from "@/lib/interface";
import { formatDate, formatTime, getStatusColor, getTypeColor, handlePrintTicket, translateOrderStatus, translateOrderType } from "@/lib/utils";
import { getOrderByOrderNumber, getOrders, getOrdersByStatus, getStats, updateOrderStatus } from "@/services/order";
import { useEffect, useState } from "react";

import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@radix-ui/react-label";
import { SearchIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { CircularProgress } from "@/components/ui/progress";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import OrderItemCard from "@/components/order/order-item-card";

const tableColumns = [
  { label: "orderNumber", sortKey: "orderNumber" },
  { label: "status", sortKey: "status" },
  { label: "type", sortKey: "type" },
  { label: "createdAt", sortKey: "createdAt" },
];

// Status summary data
const statusSummaryState = [
  {
    name: "all",
    label: "Toutes",
    count: 0,
    color: "bg-slate-100 text-gray-800 cursor-pointer",
  },
  {
    name: "PENDING",
    label: "Nouvelles commandes",
    count: 0,
    color: "bg-orange-100 text-orange-800 cursor-pointer",
  },
  {
    name: "PREPARING",
    label: "En préparation",
    count: 0,
    color: "bg-blue-100 text-blue-800 cursor-pointer",
  },
  {
    name: "READY",
    label: "Prêt à livrer",
    count: 0,
    color: "bg-green-100 text-green-800 cursor-pointer",
  },
  {
    name: "COMPLETED",
    label: "Terminées",
    count: 3,
    color: "bg-gray-100 text-gray-800 cursor-pointer",
  },
  {
    name: "CANCELLED",
    label: "Annulées",
    count: 0,
    color: "bg-red-100 text-red-800 cursor-pointer",
  },
];

const Orders = ({ trans }: any) => {
  const [ordersList, setOrdersList] = useState<any[]>([]);
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(true);
  const [statusSummary, setStatusSummary] = useState(statusSummaryState);
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [orderNumber, setOrderNumber] = useState("");
  const [onlyToday, setOnlyToday] = useState(true);
  const [sortColumn, setSortColumn] = useState<string>("createdAt");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const router = useRouter();

  // Status change modal state
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<IOrder | null>(null);

  useEffect(() => {
    fetchOrders();
    fetchStatusStats();
  }, []);

  useEffect(() => {
    fetchOrders();
    fetchStatusStats();
  }, [page, sortColumn, sortDirection]);

  useEffect(() => {
    fetchOrders();
    fetchStatusStats();
    if (selectedStatus !== "all") {
      fetchOrdersByStatus(selectedStatus);
    }
  }, [onlyToday]);

  useEffect(() => {
    const handler = setTimeout(() => {
      if (orderNumber !== "") {
        fetchOrdersByOrderNumber();
      } else {
        fetchOrders();
        fetchStatusStats();
        if (selectedStatus !== "all") {
          fetchOrdersByStatus(selectedStatus);
        }
      }
    }, 500);

    return () => clearTimeout(handler);
  }, [orderNumber]);

  const fetchStatusStats = async () => {
    const stats = await getStats(onlyToday);
    if (stats) {
      setStatusSummary(
        statusSummary.map((status) => ({
          ...status,
          count: stats[status.name.toLowerCase()],
        }))
      );
    }
  };

  const fetchOrders = async () => {
    setLoading(true);
    const orders = await getOrders(onlyToday);
    // page,
    // 10,
    // sortColumn,
    // sortDirection
    if (orders) {
      setOrdersList(orders);
      //setTotalPages(Math.ceil(orders.totalCount / 10));
    }
    setLoading(false);
  };

  const fetchOrdersByOrderNumber = async () => {
    setLoading(true);
    const orders = await getOrderByOrderNumber(orderNumber, onlyToday);
    // page,
    // 10,
    // sortColumn,
    // sortDirection
    if (orders) {
      setOrdersList(orders as any);
      //setTotalPages(Math.ceil(orders.totalCount / 10));
    }
    setLoading(false);
  };

  const fetchOrdersByStatus = async (status: string) => {
    setLoading(true);
    const orders = status === "all" ? await fetchOrders() : await getOrdersByStatus(status, onlyToday);
    // page,
    // 10,
    // sortColumn,
    // sortDirection
    if (orders) {
      setOrdersList(orders);
      //setTotalPages(Math.ceil(orders.totalCount / 10));
    }
    setLoading(false);
  };

  const toggleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleStatusChange = (status: string) => {
    setSelectedStatus(status);
    fetchOrdersByStatus(status);
    fetchStatusStats();
  };

  // Open status change modal
  const openStatusModal = (order: IOrder) => {
    setSelectedOrder(order);
    setIsStatusModalOpen(true);
  };

  // Handle order status update
  const handleOrderStatusChange = async (newStatus: OrderStatus) => {
    if (!selectedOrder) return;

    try {
      setLoading(true);
      const result = await updateOrderStatus(selectedOrder.id.toString(), newStatus);
      if (result) {
        // Refresh orders list
        fetchOrders();
        fetchStatusStats();
        if (selectedStatus !== "all") {
          fetchOrdersByStatus(selectedStatus);
        }
        // Close modal
        setIsStatusModalOpen(false);
      }
    } catch (error) {
      console.error("Error updating order status:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {/* Status Change Modal */}
      <Dialog open={isStatusModalOpen} onOpenChange={setIsStatusModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {trans?.changeOrderStatus || "Change Order Status"} - {selectedOrder?.orderNumber}
            </DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-4 py-4">
            <Button color="warning" onClick={() => handleOrderStatusChange(OrderStatus.PENDING)} variant="outline" className="w-full h-20">
              {translateOrderStatus(OrderStatus.PENDING, trans) || "Pending"}
            </Button>
            <Button color="info" onClick={() => handleOrderStatusChange(OrderStatus.PREPARING)} variant="outline" className="w-full h-20">
              {translateOrderStatus(OrderStatus.PREPARING, trans) || "Preparing"}
            </Button>
            <Button color="info" onClick={() => handleOrderStatusChange(OrderStatus.READY)} variant="outline" className="w-full h-20">
              {translateOrderStatus(OrderStatus.READY, trans) || "Ready"}
            </Button>
            <Button color="success" onClick={() => handleOrderStatusChange(OrderStatus.COMPLETED)} variant="outline" className="w-full h-20">
              {translateOrderStatus(OrderStatus.COMPLETED, trans) || "Completed"}
            </Button>
            <Button
              color="destructive"
              onClick={() => handleOrderStatusChange(OrderStatus.CANCELLED)}
              variant="outline"
              className="w-full h-20 col-span-2"
            >
              {trans?.cancelTheOrder || "Annuler la commande"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Status Summary Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-6 mb-2">
        {statusSummary.map((status) => (
          <div
            onClick={() => handleStatusChange(status.name)}
            key={status.label}
            className={`rounded-md p-2 ${status.color} ${selectedStatus === status.name ? "border-2 border-blue-900" : ""}`}
          >
            <div className="text-sm font-medium">{status.label}</div>
            <div className="mt-1 text-2xl font-bold">{status.count}</div>
          </div>
        ))}
      </div>
      {/* Filters Section */}
      <div className="rounded-lg border bg-white p-4 my-4">
        <div className="flex flex-col gap-4 md:flex-row md:items-end">
          <div className="flex-1 space-y-2">
            <Label htmlFor="search">Rechercher par numéro de commande</Label>
            <div className="relative">
              <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                onChange={(e) => setOrderNumber(e.target.value)}
                value={orderNumber}
                id="search"
                type="search"
                placeholder="Rechercher par numéro de commande..."
                className="pl-8"
              />
            </div>
          </div>
          <div className="flex items-center justify-center pb-2 space-x-2">
            <Checkbox id="today-only" onCheckedChange={(checked) => setOnlyToday(checked as boolean)} checked={onlyToday} />
            <Label htmlFor="today-only">Afficher uniquement les commandes d'aujourd'hui</Label>
          </div>
        </div>
      </div>
      {/* Orders Table */}
      {loading && ordersList.length === 0 ? (
        <div className="w-full flex justify-center items-center py-10">
          <CircularProgress value={50} color="primary" loading size="xs" />
        </div>
      ) : ordersList.length === 0 ? (
        <NoResult message={trans?.noOrders || "No orders available."} />
      ) : (
        <>
          <div className="relative">
            {/* Optional overlay spinner during background loads */}
            {loading && (
              <div className="absolute inset-0 bg-white/60 z-10 flex items-center justify-center">
                <CircularProgress value={50} color="primary" loading size="xs" />
              </div>
            )}

            <div className="flex">
              <div className={`${selectedOrder ? "w-2/3" : "w-full"}`}>
                <div
                  className=" flex-grow h-[80vh] p-2 overflow-y-auto [&::-webkit-scrollbar]:w-[5px]
                      [&::-webkit-scrollbar-track]:rounded-full
                      [&::-webkit-scrollbar-track]:bg-gray-100
                      [&::-webkit-scrollbar-thumb]:rounded-full
                      [&::-webkit-scrollbar-thumb]:bg-primary-300
                      dark:[&::-webkit-scrollbar-track]:bg-neutral-700
                      dark:[&::-webkit-scrollbar-thumb]:bg-neutral-50"
                >
                  <Table>
                    <TableHeader>
                      <TableRow>
                        {tableColumns.map((column) => (
                          <SortableTableHead
                            key={column.sortKey}
                            label={column.label}
                            sortKey={column.sortKey}
                            sortColumn={sortColumn}
                            sortDirection={sortDirection}
                            onSort={toggleSort}
                            trans={trans}
                          />
                        ))}
                        <TableHead>{trans?.action}</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody className="[&_tr:last-child]:border-1">
                      {ordersList.map((item: IOrder) => (
                        <TableRow key={item.id}>
                          <TableCell
                            className="font-medium text-card-foreground/80 cursor-pointer hover:bg-gray-50"
                            onClick={() => setSelectedOrder(item)}
                          >
                            <div className="flex gap-3 items-center">
                              <span className="text-sm text-card-foreground">{item.orderNumber}</span>
                            </div>
                          </TableCell>
                          <TableCell className="cursor-pointer   hover:bg-gray-50" onClick={() => openStatusModal(item)}>
                            <div
                              className={` inline-flex items-center border-transparent rounded-full px-2 py-[1px] text-xs font-semibold ${
                                statusSummaryState.find((s) => s.name === item.status)?.color
                              } border-2`}
                            >
                              {translateOrderStatus(item.status, trans)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div
                              className={`text-pretty inline-flex items-center border-transparent rounded-full px-2 py-[1px] text-xs font-semibold ${getTypeColor(
                                item.type as OrderType
                              )} border-2`}
                            >
                              {translateOrderType(item.type, trans)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Icon icon="heroicons:calendar" className="h-4 w-4 mr-1" />
                              {formatDate(item.createdAt)}
                              <Icon icon="heroicons:clock" className="h-4 w-4 mr-1 ml-2" />
                              {formatTime(item.createdAt)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex justify-end gap-1">
                              <Button
                                // onClick={() => router.push(`/orders/${item.id}`)}
                                onClick={() => setSelectedOrder(item)}
                                size="sm"
                                variant="outline"
                                color="secondary"
                                className="flex items-center gap-2 h-10"
                              >
                                <Icon icon="heroicons:eye" className="h-5 w-5" />
                                Voir détails
                              </Button>
                              <Button
                                onClick={() => handlePrintTicket(item.id)}
                                size="sm"
                                variant="outline"
                                color="secondary"
                                className="flex items-center gap-2 h-10"
                              >
                                <Icon icon="heroicons:printer" className="h-5 w-5" />
                                Imprimer ticket
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <TablePagination
                  currentPage={page}
                  totalPages={totalPages}
                  handlePageChange={handlePageChange}
                  canPreviousPage={page > 0}
                  canNextPage={page < totalPages - 1}
                />
              </div>

              {selectedOrder && (
                <div className="w-1/3 p-4 border rounded-lg m-2">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-bold">Commande #{selectedOrder.orderNumber}</h3>
                    <button onClick={() => setSelectedOrder(null)} className="text-gray-500 hover:text-gray-700">
                      <Icon icon="heroicons:x-mark" className="h-5 w-5" />
                    </button>
                  </div>

                  {/* Order details content - customize this based on your needs */}
                  <div className="space-y-4">
                    <OrderItemCard selectedOrder={selectedOrder} onClose={() => setSelectedOrder(null)} trans={trans} />
                  </div>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default Orders;
