"use client";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react";

import NoResult from "@/components/no-result";
import SortableTableHead from "@/components/sortable-table-head";
import TablePagination from "@/components/table-pagination";
import { Input } from "@/components/ui/input";
import { OrderStatus, OrderType } from "@/lib/interface";
import { formatDate, formatTime, getStatusColor, getTypeColor, handlePrintTicket, translateOrderStatus, translateOrderType } from "@/lib/utils";
import { getOrdersFiltred, getStats, updateOrderStatus } from "@/services/order";
import { useEffect, useState } from "react";

import OrderItemCard from "@/components/order/order-item-card";
import { Calendar } from "@/components/ui/calendar";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CircularProgress } from "@/components/ui/progress";
import { Label } from "@radix-ui/react-label";
import { format } from "date-fns";
import { CalendarIcon, SearchIcon } from "lucide-react";
import { DateRange } from "react-day-picker";
import { Badge } from "@/components/ui/badge";

const tableColumns = [
  { label: "orderNumber", sortKey: "orderNumber" },
  { label: "status", sortKey: "status" },
  { label: "type", sortKey: "type" },
  { label: "price", sortKey: "payment.price" },
  { label: "createdAt", sortKey: "createdAt" },
];

// Status summary data
const statusSummaryState = [
  {
    name: "all",
    label: "Toutes",
    count: 0,
    color: "bg-slate-100 text-gray-800 cursor-pointer",
  },
  {
    name: "PENDING",
    label: "Nouvelles commandes",
    count: 0,
    color: "bg-orange-100 text-orange-800 cursor-pointer",
  },
  {
    name: "PREPARING",
    label: "En préparation",
    count: 0,
    color: "bg-blue-100 text-blue-800 cursor-pointer",
  },
  {
    name: "READY",
    label: "Prêt à livrer",
    count: 0,
    color: "bg-green-100 text-green-800 cursor-pointer",
  },
  {
    name: "COMPLETED",
    label: "Terminées",
    count: 0,
    color: "bg-gray-100 text-gray-800 cursor-pointer",
  },
  {
    name: "CANCELLED",
    label: "Annulées",
    count: 0,
    color: "bg-red-100 text-red-800 cursor-pointer",
  },
];

const Orders = ({ trans }: any) => {
  const [ordersList, setOrdersList] = useState<any[]>([]);
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(true);
  const [statusSummary, setStatusSummary] = useState(statusSummaryState);
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [orderNumber, setOrderNumber] = useState("");
  const [onlyToday, setOnlyToday] = useState(true);
  const [sortColumn, setSortColumn] = useState<string>("createdAt");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(),
    to: new Date(),
  });

  // Modal states
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
  const [orderToUpdate, setOrderToUpdate] = useState<any | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<any | null>(null);

  useEffect(() => {
    fetchOrders();
    fetchStatusStats();
  }, []);

  useEffect(() => {
    fetchOrders();
    fetchStatusStats();
  }, [page, sortColumn, sortDirection, selectedStatus, dateRange]);

  const fetchStatusStats = async () => {
    const startDate = dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined;
    const endDate = dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined;
    const stats = await getStats(startDate, endDate);

    if (stats) {
      setStatusSummary(
        statusSummary.map((status) => ({
          ...status,
          count: stats[status.name.toLowerCase()] || 0,
        }))
      );
    }
  };

  const fetchOrders = async () => {
    setLoading(true);

    try {
      const startDate = dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined;
      const endDate = dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined;

      const orders = await getOrdersFiltred({
        status: selectedStatus !== "all" ? [selectedStatus] : undefined,

        startDate,
        endDate,
      });

      setOrdersList(orders || []);
    } catch (error) {
      console.error("Error fetching orders:", error);
      setOrdersList([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchOrdersByOrderNumber = async (value: string) => {
    setLoading(true);
    const startDate = dateRange?.from ? format(dateRange.from, "yyyy-MM-dd") : undefined;
    const endDate = dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined;

    const orders = await getOrdersFiltred({
      startDate,
      endDate,
    });

    if (!orders) return;

    setOrdersList(orders.filter((order: any) => order.orderNumber.toLowerCase().includes(value.toLowerCase())));

    setLoading(false);
  };

  const toggleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleStatusChange = (status: string) => {
    setSelectedStatus(status);
    setPage(0);
  };

  const handleOrderNumberSearch = (value: string) => {
    if (value.trim() === "") return;
    fetchOrdersByOrderNumber(value);
  };

  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
    setPage(0);
  };

  // Open status change modal
  const openStatusModal = (order: any) => {
    setSelectedOrder(order);
    setIsStatusModalOpen(true);
  };

  // Handle order status update
  const handleOrderStatusChange = async (newStatus: OrderStatus) => {
    if (!selectedOrder) return;

    try {
      setLoading(true);
      const result = await updateOrderStatus(selectedOrder.id.toString(), newStatus);
      if (result) {
        // Refresh orders list
        fetchOrders();
        fetchStatusStats();

        setIsStatusModalOpen(false);
      }
    } catch (error) {
      console.error("Error updating order status:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      {/* Order Details Modal */}
      <Dialog open={isDetailsModalOpen} onOpenChange={setIsDetailsModalOpen}>
        <DialogContent className="min-w-[70vw]">
          <DialogHeader>
            <DialogTitle className="flex items-center  gap-3">
              Détails de la commande #{selectedOrder?.orderNumber}{" "}
              <div className="flex items-center gap-2 text-sm font-medium text-default-600">
                <div className="flex items-center">
                  <Icon icon="heroicons:calendar" className="h-4 w-4 mr-1" />
                  {formatDate(selectedOrder?.createdAt)}
                  <Icon icon="heroicons:clock" className="h-4 w-4 mr-1 ml-2" />
                  {formatTime(selectedOrder?.createdAt)}
                </div>

                {/* <div className="flex items-center">
                  <Icon icon="heroicons:map-pin" className="h-4 w-4 mr-1" />
                  {selectedOrder?.branch?.name}
                </div>
                <div className="flex items-center">
                  <Icon icon="heroicons:device-tablet" className="h-4 w-4 mr-1" />
                  {selectedOrder?.kiosk ? selectedOrder?.kiosk?.name : selectedOrder?.pos?.name}
                </div> */}
              </div>
            </DialogTitle>
          </DialogHeader>

          {selectedOrder && <OrderItemCard onClose={() => setIsDetailsModalOpen(false)} orderDetails={selectedOrder} trans={trans} />}
        </DialogContent>
      </Dialog>

      {/* Status Change Modal */}
      <Dialog open={isStatusModalOpen} onOpenChange={setIsStatusModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {trans?.changeOrderStatus || "Changer le statut de la commande"} #{selectedOrder?.orderNumber}
            </DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-4 py-4">
            <Button
              color={selectedOrder?.status === OrderStatus.PENDING ? "primary" : "secondary"}
              onClick={() => handleOrderStatusChange(OrderStatus.PENDING)}
              variant="outline"
              className={`w-full h-12 flex items-center justify-center gap-2  `}
            >
              {translateOrderStatus(OrderStatus.PENDING, trans) || "Pending"}
              {selectedOrder?.status === OrderStatus.PENDING && <Icon icon="heroicons:check" className="h-5 w-5 text-primary-500 " />}
            </Button>
            <Button
              color={selectedOrder?.status === OrderStatus.PREPARING ? "primary" : "secondary"}
              onClick={() => handleOrderStatusChange(OrderStatus.PREPARING)}
              variant="outline"
              className={`w-full h-12 flex items-center justify-center gap-2  `}
            >
              {translateOrderStatus(OrderStatus.PREPARING, trans) || "Preparing"}
              {selectedOrder?.status === OrderStatus.PREPARING && <Icon icon="heroicons:check" className="h-5 w-5 text-primary-500" />}
            </Button>
            <Button
              color={selectedOrder?.status === OrderStatus.READY ? "primary" : "secondary"}
              onClick={() => handleOrderStatusChange(OrderStatus.READY)}
              variant="outline"
              className={`w-full h-12 flex items-center justify-center gap-2 `}
            >
              {translateOrderStatus(OrderStatus.READY, trans) || "Ready"}
              {selectedOrder?.status === OrderStatus.READY && <Icon icon="heroicons:check" className="h-5 w-5 text-primary-500" />}
            </Button>
            <Button
              color={selectedOrder?.status === OrderStatus.COMPLETED ? "primary" : "secondary"}
              onClick={() => handleOrderStatusChange(OrderStatus.COMPLETED)}
              variant="outline"
              className={`w-full h-12 flex items-center justify-center gap-2`}
            >
              {translateOrderStatus(OrderStatus.COMPLETED, trans) || "Completed"}
              {selectedOrder?.status === OrderStatus.COMPLETED && <Icon icon="heroicons:check" className="h-5 w-5 text-primary-500" />}
            </Button>
          </div>
          {selectedOrder?.status !== OrderStatus.CANCELLED ? (
            <Button
              color="destructive"
              onClick={() => handleOrderStatusChange(OrderStatus.CANCELLED)}
              variant="outline"
              className={`w-full h-12 flex items-center justify-center gap-2`}
            >
              {trans?.cancelTheOrder || "Annuler la commande"}
            </Button>
          ) : (
            <div className="text-sm text-muted-foreground flex items-center justify-center">
              <span>{trans?.orderCancelled || "Commande annulée"}</span>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Status Summary Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-6 mb-2">
        {statusSummary.map((status) => (
          <div
            onClick={() => handleStatusChange(status.name)}
            key={status.label}
            className={`rounded-md p-2 ${status.color} ${selectedStatus === status.name ? "border-2 border-blue-900" : ""}`}
          >
            <div className="text-sm font-medium">{status.label}</div>
            <div className="mt-1 text-2xl font-bold">{status.count || 0}</div>
          </div>
        ))}
      </div>
      {/* Filters Section */}
      <div className="rounded-lg border bg-white p-4 my-4">
        <div className="flex flex-col gap-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search by order number */}
            <div className="space-y-2">
              <Label htmlFor="search">Rechercher par numéro de commande</Label>
              <div className="relative">
                <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  onChange={(e) => handleOrderNumberSearch(e.target.value)}
                  id="search"
                  type="search"
                  placeholder="Numéro de commande"
                  className="pl-8"
                />
              </div>
            </div>

            {/* Date Range Filter */}
            <div className="space-y-2">
              <div>Période</div>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" color="secondary" size="sm" className="relative justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange?.from ? (
                      dateRange.to ? (
                        dateRange.from.getTime() === dateRange.to.getTime() ? (
                          format(dateRange.from, "dd/MM/yyyy")
                        ) : (
                          <>
                            {format(dateRange.from, "dd/MM/yyyy")} - {format(dateRange.to, "dd/MM/yyyy")}
                          </>
                        )
                      ) : (
                        format(dateRange.from, "dd/MM/yyyy")
                      )
                    ) : (
                      <span>Sélectionnez une période</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar mode="range" selected={dateRange} onSelect={handleDateRangeChange} numberOfMonths={1} disabled={{ after: new Date() }} />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>
      </div>

      {/* Orders Table */}
      {loading && ordersList.length === 0 ? (
        <div className="w-full flex justify-center items-center py-10">
          <CircularProgress value={50} color="primary" loading size="xs" />
        </div>
      ) : ordersList.length === 0 ? (
        <NoResult message={trans?.noOrders || "Aucune commande trouvée."} />
      ) : (
        <>
          <div className="relative">
            {/* Optional overlay spinner during background loads */}
            {loading && (
              <div className="absolute inset-0 bg-white/60 z-10 flex items-center justify-center">
                <CircularProgress value={50} color="primary" loading size="xs" />
              </div>
            )}

            <Table>
              <TableHeader>
                <TableRow>
                  {tableColumns.map((column) => (
                    <SortableTableHead
                      key={column.sortKey}
                      label={column.label}
                      sortKey={column.sortKey}
                      sortColumn={sortColumn}
                      sortDirection={sortDirection}
                      onSort={toggleSort}
                      trans={trans}
                    />
                  ))}
                  <TableHead>{trans?.action}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="[&_tr:last-child]:border-1">
                {ordersList.map((item: any) => (
                  <TableRow key={item.id}>
                    <TableCell
                      className="font-medium text-card-foreground/80 cursor-pointer hover:bg-gray-50 p-2"
                      onClick={() => {
                        setSelectedOrder(item);
                        setIsDetailsModalOpen(true);
                      }}
                    >
                      <div className="flex gap-3 items-center">
                        <span className="text-sm text-card-foreground">#{item.orderNumber}</span>
                      </div>
                    </TableCell>
                    <TableCell className="cursor-pointer   hover:bg-gray-50  p-2" onClick={() => openStatusModal(item)}>
                      <Badge variant="soft" color={getStatusColor(item?.status as any)} className="capitalize">
                        {translateOrderStatus(item?.status, trans)}
                      </Badge>
                    </TableCell>
                    <TableCell className="p-2">
                      <Badge variant="soft" color={getTypeColor(item?.type as any)} className="capitalize">
                        {translateOrderType(item?.type, trans)}
                      </Badge>
                    </TableCell>
                    <TableCell>{item.payment?.amount || 0} DH</TableCell>
                    <TableCell className="p-2">
                      <div className="flex items-center">
                        <Icon icon="heroicons:calendar" className="h-4 w-4 mr-1" />
                        {formatDate(item.createdAt)}
                        <Icon icon="heroicons:clock" className="h-4 w-4 mr-1 ml-2" />
                        {formatTime(item.createdAt)}
                      </div>
                    </TableCell>
                    <TableCell className="p-2">
                      <div className="flex justify-end gap-1">
                        <Button
                          onClick={() => {
                            setSelectedOrder(item);
                            setIsDetailsModalOpen(true);
                          }}
                          size="sm"
                          variant="outline"
                          color="secondary"
                          className="flex items-center gap-2 h-10"
                        >
                          <Icon icon="heroicons:eye" className="h-5 w-5" />
                          Voir détails
                        </Button>
                        <Button
                          onClick={() => handlePrintTicket(item.id)}
                          size="sm"
                          variant="outline"
                          color="secondary"
                          className="flex items-center gap-2 h-10"
                        >
                          <Icon icon="heroicons:printer" className="h-5 w-5" />
                          Imprimer ticket
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <TablePagination
            currentPage={page}
            totalPages={totalPages}
            handlePageChange={handlePageChange}
            canPreviousPage={page > 0}
            canNextPage={page < totalPages - 1}
          />
        </>
      )}
    </>
  );
};

export default Orders;
