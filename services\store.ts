import { api } from "@/config/axios.config";
import { IBranch, IStore } from "@/lib/interface";

// Function to get the list of branches by store ID
export const getBranchesByStoreId = async (storeId: string): Promise<IBranch[] | null> => {
  try {
    const response = await api.get<IBranch[]>(`/stores/${storeId}/branches`);
    return response.data;
  } catch (error) {
    console.error(`Get Branches by Store ID error for store ${storeId}:`, error);
    return null;
  }
};

// Function to get the list of stores
export const getStores = async (): Promise<IStore[] | null> => {
  try {
    const response = await api.get<IStore[]>("/stores");
    return response.data;
  } catch (error) {
    console.error("Get Stores error:", error);
    return null;
  }
};

// Function to get the list of stores with pagination
interface IStoreResponse {
  totalCount: number;
  data: IStore[];
}

export const getStoresWithPagination = async (page: number = 0, size: number = 10, sortBy: string = "name", sortDirection: "asc" | "desc" = "asc"): Promise<IStoreResponse | null> => {
  try {
    const response = await api.get<IStore[]>(`/stores?size=${size}&page=${page}&sort=${sortBy},${sortDirection}`);

    // Extract total count from headers and the data from the response
    const totalCount = parseInt(response.headers["x-total-count"]);
    const data: IStore[] = response.data;

    return { totalCount, data };
  } catch (error) {
    console.error("Get Stores with Pagination error:", error);
    return null;
  }
};

// Function to get the store by Id
export const getStoreById = async (storeId: number): Promise<IStore | null> => {
  try {
    const response = await api.get<IStore>(`/stores/${storeId}`);
    return response.data;
  } catch (error) {
    console.error("Get Store by ID error:", error);
    return null;
  }
};

// Function to create a new store
export const createStore = async (newStore: IStore): Promise<IStore | null> => {
  try {
    const response = await api.post<IStore>("/stores", newStore);
    return response.data;
  } catch (error) {
    console.error("Create store error:", error);
    return null;
  }
};

// Function to delete a store by ID
export const deleteStore = async (storeId: number): Promise<boolean> => {
  try {
    await api.delete(`/stores/${storeId}`);
    return true;
  } catch (error) {
    console.error("Delete store error:", error);
    return false;
  }
};

// Function to update an existing store by ID
export const updateStore = async (storeId: number, updatedStore: Partial<IStore>): Promise<IStore | null> => {
  try {
    const response = await api.put<IStore>(`/stores/${storeId}`, updatedStore);
    return response.data;
  } catch (error) {
    console.error("Update store error:", error);
    return null;
  }
};
