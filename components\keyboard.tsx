import { useState } from "react";
import Draggable from "react-draggable";
import Keyboard from "react-simple-keyboard";
import "react-simple-keyboard/build/css/index.css";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react";

interface Props {
  onChange: (input: string) => void;
  onKeyPress: (button: string) => void;
  className?: string;
}

export default function KeyboardComponent({
  onChange,
  onKeyPress,
  className,
}: Props) {
  const [keyboardInput, setKeyboardInput] = useState("");
  const [open, setOpen] = useState(false);
  const handleChange = (input: string) => {
    setKeyboardInput(input);
    console.log(input);
  };

  const toggleKeyboard = () => {
    setOpen(!open);
    setKeyboardInput("");
  };

  return (
    <div>
      <Button onClick={toggleKeyboard} className="m-2">
        {open ? (
          <Icon icon="mdi:keyboard-hide" className="w-6 h-6" />
        ) : (
          <Icon icon="mdi:keyboard" className="w-6 h-6" />
        )}
      </Button>
      {open && (
        <Draggable>
          <div className="w-[810px] fixed top-[10%] left-[30%] bg-[#ececec] shadow-lg rounded-lg p-2">
            <div className="flex justify-end">
              <Button onClick={toggleKeyboard} className="mb-2">
                {open ? (
                  <Icon icon="mdi:keyboard-hide" className="w-6 h-6" />
                ) : (
                  <Icon icon="mdi:keyboard" className="w-6 h-6" />
                )}
              </Button>
            </div>
            <Keyboard
              onChange={handleChange}
              onKeyPress={onKeyPress}
              className=""
              layoutName="default"
              input={keyboardInput}
            />
          </div>
        </Draggable>
      )}
    </div>
  );
}
