export async function POST(request: any) {
  try {
    return new Response(JSON.stringify({ success: true, message: "Ticket printed successfully" }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: any) {
    console.error("Printing error:", error);
    return new Response(JSON.stringify({ error: "Failed to print ticket", details: error.message }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

// import { SerialPort } from 'serialport';
// import { ReadlineParser } from '@serialport/parser-readline';

// export async function POST(request: any) {
//   try {
//     const body = await request.json();
//     const { ticket } = body;

//     if (!ticket) {
//       return new Response(JSON.stringify({ error: 'Ticket data is required' }), {
//         status: 400,
//         headers: { 'Content-Type': 'application/json' },
//       });
//     }

//     // Define printer config (update port path if needed)
//     const printerPort = new SerialPort({
//       path: 'COM1', // adjust to your printer's port
//       baudRate: 115200,
//       autoOpen: false,
//     });

//     // Create promise-based open
//     await new Promise((resolve: any, reject) => {
//       printerPort.open((err) => {
//         if (err) {
//           reject(new Error('Failed to open serial port: ' + err.message));
//         } else {
//           resolve();
//         }
//       });
//     });

//     const parser = printerPort.pipe(new ReadlineParser({ delimiter: '\n' }));

//     // Printer commands
//     const advanceAndCut = Buffer.from('1D564200', 'hex');
//     const charsTable = Buffer.from('1B7410', 'hex');
//     const tailleB = Buffer.from('1B4D01', 'hex');

//     // Helper function to write data to printer
//     const writeToPrinter = (data: any) =>
//       new Promise((resolve: any, reject) => {
//         printerPort.write(data, (err) => (err ? reject(err) : resolve()));
//       });

//     // Initialize printer
//     await writeToPrinter(charsTable);
//     await writeToPrinter(tailleB);

//     // Convert ticket data to buffer if not already
//     const ticketBuffer = Buffer.isBuffer(ticket)
//       ? ticket
//       : Buffer.from(ticket);

//     // Write ticket and cut
//     await writeToPrinter(ticketBuffer);
//     await writeToPrinter(advanceAndCut);

//     // Close the port when done
//     await new Promise((resolve: any, reject) => {
//       printerPort.close((err) => (err ? reject(err) : resolve()));
//     });

//     return new Response(
//       JSON.stringify({ success: true, message: 'Ticket printed successfully' }),
//       { status: 200, headers: { 'Content-Type': 'application/json' } }
//     );
//   } catch (error: any) {
//     console.error('Printing error:', error);
//     return new Response(
//       JSON.stringify({ error: 'Failed to print ticket', details: error.message }),
//       { status: 500, headers: { 'Content-Type': 'application/json' } }
//     );
//   }
// }
