"use client";

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Calendar, Clock, LogOut, ShoppingCart, XCircle, DollarSign, Package, Percent, Receipt } from "lucide-react";
import { useAuthStore } from "@/store";
import POSReports from "./pos-reports";
import { useEffect, useState } from "react";
import {
  getOrderSummaryByPeriod,
  getOrderTypeSummary,
  getPaymentMethodSummary,
  getStatusSummary,
  getTopSellingProducts,
} from "@/services/order-summary";
import { updatePosSession } from "@/services/pos-session";
import { signOut, useSession } from "next-auth/react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { format, formatDistance } from "date-fns";
import { fr } from "date-fns/locale";
import Loading from "@/components/ui/loading";
import toast from "react-hot-toast";

interface SessionStatsPageProps {
  trans: {
    [key: string]: string;
  };
}

interface KpiData {
  totalOrders: number;
  totalAmount: number;
  totalItemsSold: number;
  totalDiscounts: number;
  totalTaxes: number;
  averageOrderValue: number;
  canceledOrders: number;
}

export function formatDateFR(dateString: any): any {
  const date = new Date(dateString);
  return format(date, "dd-MM-yyyy HH:mm", { locale: fr });
}

export function getDurationBetweenDates(start: string, end: string): string {
  const startDate = new Date(start);
  const endDate = new Date(end);
  return formatDistance(startDate, endDate, { locale: fr });
}

const SessionStatsPageView = ({ trans }: SessionStatsPageProps) => {
  const { data: sessionData } = useSession();
  const { posSession, setUserSession, setPosSession } = useAuthStore();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [loading, setLoading] = useState(false);
  const [paymentMethodSummary, setPaymentMethodSummary] = useState<any[]>([]);
  const [orderTypeSummary, setOrderTypeSummary] = useState<any[]>([]);
  const [kpiData, setKpiData] = useState<KpiData | null>(null);
  const [topProducts, setTopProducts] = useState<any[]>([]);
  const [isCloseSessionDialogOpen, setCloseSessionDialogOpen] = useState(false);
  const [isClosingSession, setIsClosingSession] = useState(false);

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Fetch session statistics
  useEffect(() => {
    const fetchSessionStats = async () => {
      if (!posSession) return;

      setLoading(true);

      try {
        const [products, summaryData, statusData, paymentMethodData, orderTypeData] = await Promise.all([
          getTopSellingProducts(posSession.openAt, posSession.closedAt || new Date()),
          getOrderSummaryByPeriod(posSession.openAt, posSession.closedAt || new Date()),
          getStatusSummary(posSession.openAt, posSession.closedAt || new Date()),
          getPaymentMethodSummary(posSession.openAt, posSession.closedAt || new Date()),
          getOrderTypeSummary(posSession.openAt, posSession.closedAt || new Date()),
        ]);
        setTopProducts(products || []);
        setPaymentMethodSummary(paymentMethodData || []);
        setOrderTypeSummary(orderTypeData || []);

        if (summaryData) {
          const canceledCount = statusData?.CANCELLED || 0;

          setKpiData({
            totalOrders: summaryData.totalOrders || 0,
            totalAmount: summaryData.totalAmount || 0,
            totalItemsSold: summaryData.totalItemsSold || 0,
            totalDiscounts: summaryData.totalDiscounts || 0,
            totalTaxes: summaryData.totalTaxes || 0,
            averageOrderValue: summaryData.averageOrderValue || 0,
            canceledOrders: canceledCount,
          });
        }
      } catch (error) {
        console.error("Failed to fetch session stats:", error);
        toast.error("Erreur lors du chargement des statistiques");
      } finally {
        setLoading(false);
      }
    };

    fetchSessionStats();
  }, [posSession]);

  const handleCloseSession = async () => {
    if (!posSession) return;

    setIsClosingSession(true);

    try {
      const sessionBody = {
        closedAt: new Date(),
        status: "CLOSED",
      };

      await updatePosSession(posSession.id, sessionBody);

      setPosSession(null);
      setUserSession(null);

      toast.success("Session fermée avec succès");
      signOut();
    } catch (error) {
      console.error("Failed to close session:", error);
      toast.error("Erreur lors de la fermeture de la session");
    } finally {
      setIsClosingSession(false);
      setCloseSessionDialogOpen(false);
    }
  };

  if (!posSession) {
    return (
      <div className="p-4">
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">Aucune session active trouvée</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-6">
      {/* Header with session info and close button */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {sessionData && (
                <Avatar className="h-12 w-12">
                  <AvatarImage
                    src={sessionData?.user.imageUrl || "https://www.shareicon.net/data/512x512/2016/05/24/770137_man_512x512.png"}
                    alt="Profile"
                  />
                  <AvatarFallback className="bg-orange-200 text-orange-700">
                    {sessionData?.user.firstName?.[0]}
                    {sessionData?.user.lastName?.[0]}
                  </AvatarFallback>
                </Avatar>
              )}

              <h1 className="text-xl font-bold">{sessionData?.user.firstName + " " + sessionData?.user.lastName} | Statistiques de Session</h1>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{currentTime.toLocaleDateString("fr-FR")}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>
                    {currentTime.toLocaleTimeString("fr-FR", {
                      hour: "2-digit",
                      minute: "2-digit",
                      hour12: false,
                    })}
                  </span>
                </div>
              </div>
              <Button onClick={() => setCloseSessionDialogOpen(true)} color="default" className="flex items-center gap-2">
                <LogOut className="h-4 w-4" />
                Clôturer la journée du {currentTime.toLocaleDateString("fr-FR")}
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* KPI Cards */}
      {kpiData && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
          <Card>
            <CardHeader className="mb-0 flex flex-row items-center justify-between space-y-0">
              <CardTitle className="text-sm font-medium">Commandes Totales</CardTitle>
            </CardHeader>
            <CardContent className="py-2">
              <div className="text-2xl font-bold">{kpiData.totalOrders}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="mb-0 flex flex-row items-center justify-between space-y-0">
              <CardTitle className="text-sm font-medium">Commandes Annulées</CardTitle>
            </CardHeader>
            <CardContent className="py-2">
              <div className="text-2xl font-bold">{kpiData.canceledOrders}</div>
              {/* {error
            ? null
            : kpiData.canceledOrders &&
              kpiData.totalOrders && (
                <p className="text-xs text-muted-foreground mt-1">
                  ({((kpiData.canceledOrders / kpiData.totalOrders) * 100).toFixed(1)}% du total)
                </p>
              )} */}
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="mb-0 flex flex-row items-center justify-between space-y-0">
              <CardTitle className="text-sm font-medium">Montant Total</CardTitle>
            </CardHeader>
            <CardContent className="py-2">
              <div className="text-2xl font-bold"> {kpiData.totalAmount.toFixed(2)} DH</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="mb-0 flex flex-row items-center justify-between space-y-0">
              <CardTitle className="text-sm font-medium">Produits Vendus</CardTitle>
            </CardHeader>
            <CardContent className="py-2">
              <div className="text-2xl font-bold">{kpiData.totalItemsSold}</div>
            </CardContent>
          </Card>
          {orderTypeSummary.length > 0 && (
            <Card>
              <CardHeader className="mb-0 flex flex-row items-center justify-between space-y-0">
                <CardTitle className="text-sm font-medium">Type de Commandes</CardTitle>
              </CardHeader>
              <CardContent className="py-2">
                <div className="space-y-1">
                  {orderTypeSummary.map((type) => (
                    <div key={type.id} className="flex justify-between items-center">
                      <span className="text-sm">
                        {type.name === "DINE_IN" ? "Sur place" : type.name === "TAKE_AWAY" ? "A emporter" : "Livraison"}
                      </span>
                      <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-300">
                        {type.totalOrders} commandes
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* POS Rapports */}
      <Card>
        <CardHeader>
          <CardTitle>Rapports Détaillés</CardTitle>
        </CardHeader>
        <CardContent>
          <POSReports kpiData={kpiData} topProducts={topProducts} posSession={posSession} paymentMethodSummary={paymentMethodSummary} />
        </CardContent>
      </Card>

      {/* Close Session Dialog */}
      <Dialog open={isCloseSessionDialogOpen} onOpenChange={setCloseSessionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Clôturer la journée</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Êtes-vous sûr de vouloir clôturer la journée du {currentTime.toLocaleDateString("fr-FR")} ?</p>
            <p className="text-sm text-muted-foreground mt-2">Cette action fermera définitivement la session en cours et vous déconnectera.</p>
          </div>
          <DialogFooter className="gap-2">
            <Button variant="outline" onClick={() => setCloseSessionDialogOpen(false)} disabled={isClosingSession}>
              Annuler
            </Button>
            <Button color="destructive" onClick={handleCloseSession} disabled={isClosingSession} className="flex items-center gap-2">
              {isClosingSession ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  Fermeture...
                </>
              ) : (
                <>
                  <LogOut className="h-4 w-4" />
                  Clôturer
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SessionStatsPageView;
