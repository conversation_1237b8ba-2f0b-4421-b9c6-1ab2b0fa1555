"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Di<PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Calendar, Clock, LogOut, Utensils, Wallet } from "lucide-react";
import { useAuthStore } from "@/store";
import { useRouter } from "next/navigation";
import POSReports from "./pos-reports";
import { useEffect, useState } from "react";
import { getOrderSummaryByPeriod, getStatusSummary, getTopSellingProducts } from "@/services/order-summary";
import { updatePosSession } from "@/services/pos-session";
import { signOut, useSession } from "next-auth/react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { format, formatDistance } from "date-fns";
import { fr } from "date-fns/locale";
import Loading from "@/components/ui/loading";
import toast from "react-hot-toast";

interface SessionStatsPageProps {
  trans: {
    [key: string]: string;
  };
}

export function formatDateFR(dateString: string): string {
  const date = new Date(dateString);
  return format(date, "dd-MM-yyyy HH:mm", { locale: fr });
}

export function getDurationBetweenDates(start: string, end: string): string {
  const startDate = new Date(start);
  const endDate = new Date(end);
  return formatDistance(startDate, endDate, { locale: fr });
}

const SessionStatsPageView = ({ trans }: SessionStatsPageProps) => {
  const { data: sessionData } = useSession();
  const router = useRouter();
  const { posSession, setUserSession, setPosSession } = useAuthStore();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [loading, setLoading] = useState(false);
  const [kpiData, setKpiData] = useState<any>(null);
  const [topProducts, setTopProducts] = useState<any[]>([]);
  const [isCloseSessionDialogOpen, setCloseSessionDialogOpen] = useState(false);
  const [isClosingSession, setIsClosingSession] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    const fetchSessionStats = async () => {
      if (!posSession) return;

      setLoading(true);

      try {
        const [products, summaryData, statusData] = await Promise.all([
          getTopSellingProducts(posSession.openAt, posSession.closedAt),
          getOrderSummaryByPeriod(posSession.openAt, posSession.closedAt),
          getStatusSummary(posSession.openAt, posSession.closedAt),
        ]);
        setTopProducts(products || []);

        if (summaryData) {
          const canceledCount = statusData?.CANCELLED || 0;

          setKpiData({
            totalOrders: summaryData.totalOrders,
            totalAmount: summaryData.totalAmount,
            totalItemsSold: summaryData.totalItemsSold,
            totalDiscounts: summaryData.totalDiscounts,
            totalTaxes: summaryData.totalTaxes,
            averageOrderValue: summaryData.averageOrderValue,
            canceledOrders: canceledCount,
          });
        }
      } catch (error) {
        console.error("Failed to fetch session stats:", error);
        toast.error("Erreur lors du chargement des statistiques");
      } finally {
        setLoading(false);
      }
    };

    fetchSessionStats();
  }, [posSession]);

  const handleCloseSession = async () => {
    if (!posSession) return;

    setIsClosingSession(true);

    try {
      const sessionBody = {
        closedAt: new Date(),
        status: "CLOSED",
      };

      await updatePosSession(posSession.id, sessionBody);

      setPosSession(null);
      setUserSession(null);

      toast.success("Session fermée avec succès");
      signOut();
    } catch (error) {
      console.error("Failed to close session:", error);
      toast.error("Erreur lors de la fermeture de la session");
    } finally {
      setIsClosingSession(false);
      setCloseSessionDialogOpen(false);
    }
  };

  const cashierData = {
    id: userSession?.user?.id || "C001",
    name: userSession?.user ? `${userSession.user.firstName} ${userSession.user.lastName}` : "John Doe",
    avatar: userSession?.user?.imageUrl || "/placeholder.svg?height=40&width=40",
    totalSales: kpiData?.totalAmount || 0,
    transactionCount: kpiData?.totalOrders || 0,
    averageTransactionValue: kpiData?.averageOrderValue || 0,
  };

  const sessionInfo = {
    startTime: sessionData?.openAt || "2023-04-15T09:00:00",
    endTime: sessionData?.closedAt || "2023-04-15T17:00:00",
    duration: "8 hours", // This could be calculated from start/end times
  };

  const cashDrawerData = {
    startingFloat: 200, // This would come from session opening data
    totalCollected: kpiData?.totalAmount || 0,
    currentBalance: (kpiData?.totalAmount || 0) + 200,
  };

  const topSellingItems = topProducts?.slice(0, 5) || [{ name: "Aucun produit", quantity: 0, revenue: 0 }];

  const discountData = {
    total: kpiData?.totalDiscounts || 0,
    breakdown: [{ type: "Remises diverses", count: 1, value: kpiData?.totalDiscounts || 0 }],
  };

  // Mock data for restaurant tables summary (would need real API)
  const tablesSummaryData = {
    totalTables: 20,
    occupiedTables: 15,
    averageTurnoverTime: "1 hour 30 minutes",
    topPerformingTable: {
      number: 7,
      revenue: 450.25,
      turnoverCount: 6,
    },
    tableDetails: [
      { number: 1, status: "Occupied", revenue: 120.5, turnoverCount: 3 },
      { number: 2, status: "Available", revenue: 85.75, turnoverCount: 2 },
      { number: 3, status: "Occupied", revenue: 200.0, turnoverCount: 4 },
      { number: 4, status: "Reserved", revenue: 0, turnoverCount: 0 },
      { number: 5, status: "Occupied", revenue: 150.25, turnoverCount: 3 },
    ],
  };

  if (!posSession) {
    return (
      <div className="p-4">
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">Aucune session active trouvée</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-6">
      {/* Header with session info and close button */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div>
                <h1 className="text-xl font-bold">Statistiques de Session</h1>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>{currentTime.toLocaleDateString("fr-FR")}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>
                      {currentTime.toLocaleTimeString("fr-FR", {
                        hour: "2-digit",
                        minute: "2-digit",
                        hour12: false,
                      })}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <Button onClick={() => setCloseSessionDialogOpen(true)} className="flex items-center gap-2">
              <LogOut className="h-4 w-4" />
              Clôturer la journée du {currentTime.toLocaleDateString("fr-FR")}
            </Button>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Session Information */}
      <Card>
        <CardHeader>
          <CardTitle>Informations de la Session</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <span className="font-medium">Ouverture:</span>
              <br />
              <span className="text-sm text-muted-foreground">{formatDateFR(posSession.openAt)}</span>
            </div>
            <div>
              <span className="font-medium">Fermeture:</span>
              <br />
              <span className="text-sm text-muted-foreground">{formatDateFR(posSession.closedAt)}</span>
            </div>
            <div>
              <span className="font-medium">Durée:</span>
              <br />
              <Badge variant="outline" className="bg-orange-100 text-orange-700 hover:bg-orange-200">
                {getDurationBetweenDates(posSession.openAt, posSession.closedAt)}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* KPI Cards */}
      {loading ? (
        <div className="flex justify-center p-8">
          <Loading />
        </div>
      ) : (
        kpiData && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card className="bg-white">
              <CardContent className="p-4 flex flex-col items-center">
                <span className="text-sm">Commandes Totales</span>
                <div className="flex items-center">
                  <span className="text-lg font-bold text-green-600">{kpiData.totalOrders}</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white">
              <CardContent className="p-4 flex flex-col items-center">
                <span className="text-sm">Commandes Annulées</span>
                <div className="flex items-center">
                  <span className="text-xl font-bold">{kpiData.canceledOrders}</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white">
              <CardContent className="p-4 flex flex-col items-center">
                <span className="text-sm">Montant Total</span>
                <div className="flex items-center">
                  <span className="text-xl font-bold">{kpiData.totalAmount.toFixed(2)} DH</span>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white">
              <CardContent className="p-4 flex flex-col items-center">
                <span className="text-sm">Articles Vendus</span>
                <div className="flex items-center">
                  <span className="text-xl font-bold">{kpiData.totalItemsSold}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        )
      )}

      {/* POS Reports */}
      <Card>
        <CardHeader>
          <CardTitle>Rapports Détaillés</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {/* Cashier Performance Section */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center gap-4">
                  <Avatar className=" w-9 h-9">
                    <AvatarImage className="" src={cashierData.avatar} alt={cashierData.name} />
                    <AvatarFallback>
                      {cashierData.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <h3 className="text-md font-semibold">
                    <p>{cashierData.name}</p>
                    <p className="text-xs text-muted-foreground">ID: {cashierData.id}</p>
                  </h3>
                </div>
              </CardHeader>
              <CardContent>
                {/* <div className="flex items-center space-x-4">
              <Avatar>
                <AvatarImage src={cashierData.avatar} alt={cashierData.name} />
                <AvatarFallback>
                  {cashierData.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium">{cashierData.name}</p>
                <p className="text-xs text-muted-foreground">
                  ID: {cashierData.id}
                </p>
              </div>
            </div> */}
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between">
                    <p className="text-sm">Ventes Totales:</p>
                    <p className="text-sm font-medium">{cashierData.totalSales.toFixed(2)} DH</p>
                  </div>
                  <div className="flex justify-between">
                    <p className="text-sm">Transactions:</p>
                    <p className="text-sm font-medium">{cashierData.transactionCount}</p>
                  </div>
                  <div className="flex justify-between">
                    <p className="text-sm">Moyenne Transaction:</p>
                    <p className="text-sm font-medium">{cashierData.averageTransactionValue.toFixed(2)} DH</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Session Information */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center gap-4">
                  <div className="bg-green-100 p-2 rounded-full">
                    <Clock className="w-5 h-5 text-green-600" />
                  </div>
                  <h3 className="text-md font-semibold">Informations de Session</h3>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <p className="text-sm">Heure de Début:</p>
                    <p className="text-sm font-medium">
                      {new Date(sessionInfo.startTime).toLocaleString("fr-FR", {
                        year: "numeric",
                        month: "2-digit",
                        day: "2-digit",
                        hour: "2-digit",
                        minute: "2-digit",
                        hour12: false,
                      })}
                    </p>
                  </div>
                  <div className="flex justify-between">
                    <p className="text-sm">Heure de Fin:</p>
                    <p className="text-sm font-medium">
                      {new Date(sessionInfo.endTime).toLocaleString("fr-FR", {
                        year: "numeric",
                        month: "2-digit",
                        day: "2-digit",
                        hour: "2-digit",
                        minute: "2-digit",
                        hour12: false,
                      })}
                    </p>
                  </div>
                  <div className="flex justify-between">
                    <p className="text-sm">Durée:</p>
                    <p className="text-sm font-medium">{sessionInfo.duration}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Cash Drawer Details */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center gap-4">
                  <div className="bg-yellow-100 p-2 rounded-full">
                    <Wallet className="w-5 h-5 text-yellow-600" />
                  </div>
                  <h3 className="text-md font-semibold">Détails du Tiroir Caissier</h3>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <p className="text-sm">Fonds de Départ:</p>
                    <p className="text-sm font-medium">{cashDrawerData.startingFloat.toFixed(2)} DH</p>
                  </div>
                  <div className="flex justify-between">
                    <p className="text-sm">Total Collecté:</p>
                    <p className="text-sm font-medium">{cashDrawerData.totalCollected.toFixed(2)} DH</p>
                  </div>
                  <div className="flex justify-between">
                    <p className="text-sm">Solde Actuel:</p>
                    <p className="text-sm font-medium">{cashDrawerData.currentBalance.toFixed(2)} DH</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Restaurant Tables Summary */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center gap-4">
                  <div className="bg-primary-100 p-2 rounded-full">
                    <Utensils className="w-5 h-5 text-primary-600" />
                  </div>
                  <h3 className="text-md font-semibold">Résumé des Tables du Restaurant</h3>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <p className="text-sm">Tables Totales:</p>
                    <p className="text-sm font-medium">{tablesSummaryData.totalTables}</p>
                  </div>
                  <div className="flex justify-between">
                    <p className="text-sm">Tables Occupées:</p>
                    <p className="text-sm font-medium">{tablesSummaryData.occupiedTables}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Close Session Dialog */}
      <Dialog open={isCloseSessionDialogOpen} onOpenChange={setCloseSessionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Clôturer la journée</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>Êtes-vous sûr de vouloir clôturer la journée du {currentTime.toLocaleDateString("fr-FR")} ?</p>
            <p className="text-sm text-muted-foreground mt-2">Cette action fermera définitivement la session en cours et vous déconnectera.</p>
          </div>
          <DialogFooter className="gap-2">
            <Button variant="outline" onClick={() => setCloseSessionDialogOpen(false)} disabled={isClosingSession}>
              Annuler
            </Button>
            <Button variant="destructive" onClick={handleCloseSession} disabled={isClosingSession} className="flex items-center gap-2">
              {isClosingSession ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  Fermeture...
                </>
              ) : (
                <>
                  <LogOut className="h-4 w-4" />
                  Clôturer
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SessionStatsPageView;
