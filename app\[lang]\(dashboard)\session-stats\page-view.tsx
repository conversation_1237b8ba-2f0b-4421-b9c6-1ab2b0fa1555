"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Calendar, Clock } from "lucide-react";
import { useAuthStore } from "@/store";
import { useRouter } from "next/navigation";
import POSReports from "./pos-reports";
import { useEffect, useState } from "react";
import KeyboardComponent from "@/components/keyboard";

interface SessionStatsPageProps {
  trans: {
    [key: string]: string;
  };
}

const SessionStatsPageView = ({ trans }: SessionStatsPageProps) => {
  const [currentTime, setCurrentTime] = useState(new Date("2024-12-13T13:05:35+01:00"));

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="p-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            POS Reports
            <div className="flex items-center gap-2 text-sm">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>{currentTime.toLocaleDateString("fr-FR")}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span>
                  {currentTime.toLocaleTimeString("fr-FR", {
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: false,
                  })}
                </span>
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <POSReports />
        </CardContent>
      </Card>
    </div>
  );
};

export default SessionStatsPageView;
