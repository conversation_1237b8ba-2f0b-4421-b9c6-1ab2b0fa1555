import { api } from "@/config/axios.config";
import { IVariation, NewVariationType } from "@/lib/interface";

// Function to get the list of ingredients
export const getVariations = async (): Promise<IVariation[] | null> => {
  try {
    const response = await api.get<IVariation[]>(`/variations/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}?availability=true&size=500`);
    return response.data;
  } catch (error) {
    console.error("Get ingredients error:", error);
    return null;
  }
};

// Function to get the list of ingredients
interface IVariationResponse {
  totalCount: number;
  data: IVariation[];
}

export const getVariationsWithPagination = async (
  page: number = 0,
  size: number = 10,
  sortBy: string = "name",
  sortDirection: "asc" | "desc" = "asc"
): Promise<IVariationResponse | null> => {
  try {
    const response = await api.get<IVariation[]>(
      `/variations/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}?size=${size}&page=${page}&sort=${sortBy},${sortDirection}`
    );

    // Extract total count from headers and the data from the response
    const totalCount = parseInt(response.headers["x-total-count"]);
    const data: IVariation[] = response.data;

    return { totalCount, data };
  } catch (error) {
    console.error("Get ingredients error:", error);
    return null;
  }
};

// Function to get the ingredient by id
export const getVariationById = async (modifierId: number): Promise<IVariation | null> => {
  try {
    const response = await api.get<IVariation>(`/variations/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/${modifierId}`);
    return response.data;
  } catch (error) {
    console.error("Get ingredient error:", error);
    return null;
  }
};

// Function to get the list of ingredients by modifierId
export const getVariationsByModifierId = async (modifierId: number): Promise<IVariation[] | null> => {
  try {
    const response = await api.get<IVariation[]>(`/variations/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/branch/1/modifier/${modifierId}`);
    return response.data;
  } catch (error) {
    console.error("Get ingredients error:", error);
    return null;
  }
};

// Function to create a new Variation
export const createVariation = async (newVariation: NewVariationType): Promise<IVariation | null> => {
  try {
    const response = await api.post<IVariation>(`/variations/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}`, newVariation);
    return response.data;
  } catch (error) {
    console.error("Create Variation error:", error);
    return null;
  }
};

// Function to delete a Variation by ID
export const deleteVariation = async (ingredientId: number): Promise<boolean> => {
  try {
    await api.delete(`/variations/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/${ingredientId}`);
    return true;
  } catch (error) {
    console.error("Delete Variation error:", error);
    return false;
  }
};

// Function to update an existing Variation by ID
export const updateVariation = async (ingredientId: number, updatedVariation: any): Promise<IVariation | null> => {
  try {
    const response = await api.patch<IVariation>(`/variations/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/${ingredientId}`, updatedVariation);
    return response.data;
  } catch (error) {
    console.error("Update Variation error:", error);
    return null;
  }
};
