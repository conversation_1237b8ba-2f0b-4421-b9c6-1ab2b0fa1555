"use client";

import Image from "next/image";
import auth3Light from "@/public/images/auth/auth3-light.png";
import auth3Dark from "@/public/images/auth/auth3-dark.png";
import LoginSessionCodeForm from "@/components/auth/login-session-code";
import SiteLogoView from "@/components/site-logo";

const LoginPageView = ({ trans }: any) => {
  return (
    <div className="loginwrapper flex justify-center items-center relative overflow-hidden">
      {/* Background Images */}
      <Image src={auth3Dark} alt="image de fond" className="absolute top-0 left-0 w-full h-full light:hidden" />
      <Image src={auth3Light} alt="image de fond" className="absolute top-0 left-0 w-full h-full dark:hidden" />

      {/* Tabs Component */}
      <div className="w-full h-[660px] bg-background py-5 max-w-4xl rounded-xl relative z-10 2xl:p-16 xl:p-12 p-10 m-4">
        <SiteLogoView className="h-16 mx-auto mb-8" />
        <div className="grid grid-cols-2 gap-4 w-full">
          {/* Badge Scanner Content */}

          <div className="w-full h-[250px] px-5">
            <BadgeScanner trans={trans} />
          </div>

          {/* Login Form Content */}

          <div className="w-full h-[250px]  px-5">
            {/* <LogInForm trans={trans} /> */}
            <div className="text-center w-full ">
              <h2 className="mb-4 text-xl font-bold">Saisir le Code d'Accès</h2>
              <p className="mb-4">Utilisez le pavé numérique pour entrer votre code à 4 chiffres.</p>
            </div>
            <LoginSessionCodeForm inputType="password" />
          </div>
        </div>
      </div>
    </div>
  );
};

const BadgeScanner = ({ trans }: any) => {
  return (
    <div className="text-center w-full h-full flex flex-col justify-center pt-1">
      <style jsx>{`
        @keyframes scan {
          0% {
            transform: scale(1);
            opacity: 1;
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
          }
          50% {
            transform: scale(1.05);
            opacity: 0.8;
            box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
          }
          100% {
            transform: scale(1);
            opacity: 1;
            box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
          }
        }
        .scan-animation {
          animation: scan 2s infinite;
          border-radius: 50%;
        }
      `}</style>
      <h2 className="mb-4  text-xl font-bold">Scan du Badge</h2>
      <p className="mb-4">Placez votre badge près du scanner pour vous connecter.</p>
      <img src="/images/all-img/badge-icon-1.jpg" className="w-40 h-40 mx-auto flex items-center justify-center scan-animation" />
    </div>
  );
};

export default LoginPageView;
