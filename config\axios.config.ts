// config/axios.config.js
import axios from "axios";
import { getSession, signOut } from "next-auth/react";
import { jwtDecode } from "jwt-decode"; // Install this package: npm install jwt-decode

const baseURL = process.env.NEXT_PUBLIC_SITE_URL + "/api";

const api = axios.create({
  baseURL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add a request interceptor to include the Bearer token
api.interceptors.request.use(
  async (config) => {
    const session = await getSession();

    // Check if the session exists and has a token
    if (session?.user?.token) {
      try {
        // Decode the JWT to get the token's expiration time (exp)
        const decodedToken: any = jwtDecode(session.user.token);
        const tokenExp = decodedToken?.exp * 1000;

        // Check if the token is expired
        if (Date.now() >= tokenExp) {
          console.log("Token expired. Logging out the user.");

          // Use NextAuth's signOut to log the user out
          await signOut({ redirect: false });

          // Optionally, cancel the request
          // throw new axios.Cancel("Token expired");
        } else {
          // If the token is still valid, add it to the headers
          config.headers.Authorization = `Bearer ${session.user.token}`;
        }
      } catch (error) {
        console.error("Failed to decode the token:", error);
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export { api };
