import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react";

const basketColumns = [
  { id: 1, label: "Produit" },
  { id: 2, label: "Quantité" },
  { id: 3, label: "Prix" },
  { id: 4, label: "" },
];

export const BasketTable = ({ basketTableItems, onIncrement, onDecrement, onDelete }: any) => {
  return (
    <Table className="">
      <TableHeader>
        <TableRow>
          {basketColumns.map((column, index) => (
            <TableHead key={`basket-table-header-${index}`} className="text-xs h-8 p-0 text-default-600 ltr:last:text-right rtl:last:text-left">
              {column.label}
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody className="[&_tr:last-child]:border-1 py-2 mt-0">
        {basketTableItems.map((item: any, index: number) => (
          <TableRow key={`basket-item-${index}`} className="py-2">
            <TableCell className="text-xs font-medium text-default-600 max-w-[200px] truncate p-0 py-1">
              <div className="text-xs text-card-foreground">{item.product.name}</div>
              {/* <div>
                {item.variations.map((item: any, index: number) => (
                  <div key={`item-${index}`} className="text-xs font-light">
                    {item.option.name}
                  </div>
                ))}
              </div> */}
            </TableCell>
            <TableCell className="text-xs font-medium text-default-600 p-0 py-1 flex items-center gap-2">
              <Button
                size="icon"
                variant="outline"
                className="h-6 w-6"
                color="primary"
                onClick={() => (item.qty === 1 ? onDelete(index) : onDecrement(index))}
              >
                <Icon icon="heroicons:minus" className="h-4 w-4" />
              </Button>
              <span>{item.qty}</span>
              <Button size="icon" variant="outline" className="h-6 w-6" color="primary" onClick={() => onIncrement(index)}>
                <Icon icon="heroicons:plus" className="h-4 w-4" />
              </Button>
            </TableCell>
            <TableCell className="text-xs font-medium text-default-600 whitespace-nowrap p-0 py-1">{item.amount} DH</TableCell>
            <TableCell className="py-0">
              <Button size="icon" variant="outline" className="h-6 w-6" color="destructive" onClick={() => onDelete(index)}>
                <Icon icon="heroicons:trash" className="h-4 w-4" />
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};
