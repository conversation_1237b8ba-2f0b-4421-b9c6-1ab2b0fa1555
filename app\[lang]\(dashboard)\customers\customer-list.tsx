import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON>alogTitle, DialogTrigger, DialogClose, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { InputGroup, InputGroupText } from "@/components/ui/input-group";
import { useEffect, useState } from "react";
import { ICustomer } from "@/lib/interface";
import { getCustomers } from "@/services/customer";
import Loading from "@/components/ui/loading";
import NoResult from "@/components/no-result";

interface CustomersPageViewProps {
  trans: {
    [key: string]: string;
  };
}

const tableColumns = [
  { label: "category", sortKey: "name" },
  { label: "description", sortKey: "description" },
  { label: "availability", sortKey: "availabilityStatus" },
  { label: "availableFrom", sortKey: "availableFrom" },
  { label: "availableTo", sortKey: "availableTo" },
];

const Customers = ({ trans }: CustomersPageViewProps) => {
  const [customersList, setCustomersList] = useState<ICustomer[]>([]);
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState<boolean>(true);
  const [sortColumn, setSortColumn] = useState<string>("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  useEffect(() => {
    fetchCustomers();
  }, []);

  useEffect(() => {
    fetchCustomers();
  }, [page, sortColumn, sortDirection]);

  const fetchCustomers = async () => {
    setLoading(true);
    const customers = await getCustomers();
    // page,
    // 10,
    // sortColumn,
    // sortDirection
    if (customers) {
      setCustomersList(customers);
      //setTotalPages(Math.ceil(customers.totalCount / 10));
    }
    setLoading(false);
  };
  if (loading) {
    return <Loading />;
  }

  if (customersList.length === 0) {
    return <NoResult message={trans?.noCustomers || "No customers available."} />;
  }
  return (
    <div>
      <InputGroup merged className="flex-none max-w-[248px]">
        <InputGroupText>
          <Icon icon="heroicons:magnifying-glass" />
        </InputGroupText>
        <Input type="text" placeholder="Search.." />
      </InputGroup>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="font-semibold">Client</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Adresse</TableHead>
            <TableHead>Téléphone</TableHead>
            {/* <TableHead>Action</TableHead> */}
          </TableRow>
        </TableHeader>
        <TableBody>
          {customersList.map((item: ICustomer) => (
            <TableRow key={item.id}>
              <TableCell className="font-medium text-card-foreground/80">
                {item.firstName} {item.lastName}
              </TableCell>
              <TableCell>{item.email}</TableCell>
              <TableCell>{item.address}</TableCell>
              <TableCell>{item.phone}</TableCell>

              {/* <TableCell>
                <div className="flex justify-end gap-3">
                  <EditingDialog />
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        size="icon"
                        variant="outline"
                        className=" h-7 w-7"
                        color="secondary"
                      >
                        <Icon icon="heroicons:trash" className=" h-4 w-4  " />
                      </Button>
                    </DialogTrigger>
                    <DialogContent size="md">
                      <DialogHeader>
                        <DialogTitle className="text-base font-medium text-default-700 ">
                          Confirm Deletion
                        </DialogTitle>
                      </DialogHeader>
                      <div className="text-sm text-default-500  space-y-4">
                        <p>
                          <span className="text-destructive font-medium">
                            Warning:
                          </span>{" "}
                          Are you sure you want to delete this item? This action
                          cannot be undone.
                        </p>
                      </div>
                      <DialogFooter className="mt-8">
                        <DialogClose asChild>
                          <Button variant="ghost" color="secondary">
                            Cancel
                          </Button>
                        </DialogClose>
                        <Button color="destructive">Delete</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </TableCell> */}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default Customers;

const EditingDialog = () => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button disabled size="icon" variant="outline" color="secondary" className=" h-7 w-7">
          <Icon icon="heroicons:pencil" className=" h-4 w-4  " />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Item</DialogTitle>
          <form action="#" className=" space-y-5 pt-4">
            <div>
              <Label className="mb-2">Name</Label>
              <Input placeholder="Name" />
            </div>
            {/* end single */}
            <div>
              <Label className="mb-2">Title</Label>
              <Input placeholder="Title" />
            </div>
            {/* end single */}
            <div>
              <Label className="mb-2">Email</Label>
              <Input placeholder="Email" type="email" />
            </div>
            {/* end single */}
            <div>
              <Label className="mb-2">Email</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">Admin</SelectItem>
                  <SelectItem value="dark">Owner</SelectItem>
                  <SelectItem value="system">Member</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {/* end single */}
            <div className="flex justify-end space-x-3">
              <DialogClose asChild>
                <Button type="button" variant="outline" color="destructive">
                  Cancel
                </Button>
              </DialogClose>
              <DialogClose asChild>
                <Button color="success">Save</Button>
              </DialogClose>
            </div>
          </form>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
};
