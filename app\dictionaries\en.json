{"dashboard": "Dashboard", "analytics": "Analytics", "ecommerce": "ecommerce", "project": "project", "chat": "chat", "email": "Email", "kanban": "kanban", "task": "task", "calender": "calendar", "projects": "projects", "base ui": "base UI", "accordion": "accordion", "alert": "alert", "avatar": "avatar", "badge": "badge", "breadcrumb": "breadcrumb", "button": "button", "card": "card", "carousel": "carousel", "color": "color", "combobox": "combobox", "command": "command", "dropdown": "dropdown", "dialog": "dialog", "kbd": "kbd", "pagination": "pagination", "popover": "popover", "progress": "progress", "sheet": "sheet", "skeleton": "skeleton", "tabs": "tabs", "toast": "toast", "tooltip": "tooltip", "typography": "typography", "affix": "affix", "calendar": "calendar", "steps": "steps", "timeline": "timeline", "tour": "tour", "tree": "tree", "watermark": "watermark", "autocomplete": "autocomplete", "checkbox": "checkbox", "file uploader": "file uploader", "input": "input", "input group": "input group", "input mask": "input mask", "radio": "radio", "range slider": "range slider", "rating": "rating", "select": "select", "react select": "react select", "switch": "switch", "textarea": "textarea", "form layout": "form layout", "form elemenets": "form elements", "form validation": "form validation", "use controller": "use controller", "use form": "use form", "form wizard": "form wizard", "pages": "pages", "error": "error", "utility": "utility", "invoice": "invoice", "sign in 01": "sign in 01", "sign in 02": "sign in 02", "sign in 03": "sign in 03", "sign in 04": "sign in 04", "sign in 05": "sign in 05", "sign up 01": "sign up 01", "sign up 02": "sign up 02", "sign up 03": "sign up 03", "sign up 04": "sign up 04", "sign up 05": "sign up 05", "forget password 01": "forget password 01", "forget password 02": "forget password 02", "forget password 03": "forget password 03", "forget password 04": "forget password 04", "forget password 05": "forget password 05", "lock screen 01": "lock screen 01", "lock screen 02": "lock screen 02", "lock screen 03": "lock screen 03", "lock screen 04": "lock screen 04", "lock screen 05": "lock screen 05", "two step 01": "two step 01", "two step 02": "two step 02", "two step 03": "two step 03", "two step 04": "two step 04", "two step 05": "two step 05", "password create 01": "password create 01", "password create 02": "password create 02", "password create 03": "password create 03", "password create 04": "password create 04", "password create 05": "password create 05", "error 401": "error 401", "error 403": "error 403", "error 404": "error 404", "error 419": "error 419", "error 429": "error 429", "error 500": "error 500", "error 503": "error 503", "blank page": "blank page", "comming soon": "coming soon", "under maintenance": "under maintenance", "user profile": "user profile", "overview": "overview", "documents": "documents", "activities": "activities", "settings": "settings", "invoices": "invoices", "create invoice": "create invoice", "invoice list": "invoice list", "email template": "email template", "welcome": "welcome", "authentication": "authentication", "advanced": "advanced", "basic": "basic", "reset password 01": "reset password 01", "reset password 02": "reset password 02", "verify email": "verify email", "verify otp": "verify otp", "shop": "shop", "shopping cart": "shopping cart", "corporate": "corporate", "agency": "agency", "blog": "blog", "photography": "photography", "tables": "Tables", "simple table": "simple table", "tailwindui table": "tailwindui table", "data table": "data table", "diagram": "diagrams", "react flow": "react flow", "organization tree": "organization tree", "update node": "update node", "add node": "add node", "horizontal flow": "horizontal flow", "dagree tree": "dagree tree", "download diagram": "download diagram", "with minimap": "with minimap", "with background": "with background", "panel position": "panel position", "chart": "chart", "apex chart": "apex chart", "rechart chart": "rechart chart", "chart js": "chart js", "unovis": "unovis", "line": "line", "area": "area", "column": "column", "bar": "bar", "combo/mixed": "combo/mixed", "Range Area": "Range Area", "funnel": "funnel", "candle stick": "candle stick", "boxplot": "boxplot", "pie": "pie", "radar": "radar", "polar area": "polar area", "radial bars": "radial bars", "bubble": "bubble", "scatter": "scatter", "heatmap": "heatmap", "treemap": "treemap", "composed": "composed", "tree map": "tree map", "other": "other", "scales": "scales", "scale options": "scale options", "legend": "legend", "title": "title", "subtitle": "subtitle", "scriptable options": "scriptable options", "animations": "animations", "maps": "maps", "google": "google", "vector": "vector", "react leaflet": "react leaflet", "unovis map": "unovis map", "leaflet map": "leaflet map", "leaflet flow": "leaflet flow", "leaflet advance": "leaflet advance", "icons": "icons", "hero icons": "hero icons", "lucide icons": "lucide icons", "custom icons": "custom icons", "multi level": "multi level", "level 1.1": "level 1.1", "level 2": "level 2", "application": "application", "components": "components", "forms": "forms", "menu": "menu", "addCategory": "Add Category", "category": "Category", "description": "Description", "availability": "Availability", "availableFrom": "Available From", "availableTo": "Available To", "action": "Action", "inStock": "In Stock", "outOfStock": "Out of Stock", "confirmDeletion": "Confirm Deletion", "warning": "Warning", "deleteConfirmation": "Are you sure you want to delete this item? This action is irreversible.", "cancel": "Cancel", "delete": "Delete", "categoryDeleted": "Category deleted!", "deleteError": "Error deleting category", "products": "Products", "product": "Product", "list": "List", "addons": "Addons", "add product": "Add Product", "add": "Add", "modify": "Modify", "categories": "Categories", "add category": "Add Category", "selectCategory": "--Select a category--", "variations": "Variations", "variation": "Variation", "add variation": "Add Variation", "options": "Options", "financial": "Financial", "coupons": "Coupons", "discounts": "Discounts", "taxes": "Taxes", "store": "Store", "categoryName": "Category Name", "startTimeAvailability": "Start Time Availability", "endTimeAvailability": "End Time Availability", "uploadDescription": "Drop the image here or click to upload.", "categoryImage": "Category Image (maximum size 1MB to 3.5MB)", "reset": "Reset", "finish": "Finish", "categoryCreated": "Category Created!", "errorCreatingCategory": "Error Creating Category", "addVariation": "Add Variation", "price": "Price", "productDeleted": "Product deleted!", "deletionError": "Error while deleting the product", "addProduct": "Add Product", "isAddon": "<PERSON><PERSON>", "productAddedSuccess": "Product added successfully!", "productCreationError": "Error while creating a new product", "productNameLabel": "Product name", "productImageHeader": "Product image (maximum size of 1MB to 3.5MB)", "availabilityHeader": "Availability", "availabilityFromLabel": "Start of availability", "availabilityToLabel": "End of availability", "financialInfoHeader": "Financial information", "discountLabel": "Discount", "taxLabel": "Tax", "done": "Done", "next": "Next", "assignmentOptionsVariations": "Assigning options and variations to the product", "assignmentSuccess": "Options and variations have been successfully assigned to the product", "option": "Option", "addAssignment": "Add Assignment", "maxAllowed": "<PERSON> Allowed", "selectOption": "Select option", "isDefault": "Is default", "editProduct": "Edit product", "productUpdatedSuccess": "Product updated successfully!", "productUpdateError": "Error updating the product", "variationName": "Variation Name", "variationCreateError": "Error creating the new variation", "variationAddedSuccess": "Variation added successfully!", "variationImage": "Variation image (maximum size 1 MB to 3.5 MB)", "editVariation": "Edit Variation", "variationUpdateError": "Error updating the variation", "variationUpdatedSuccess": "Variation updated successfully!", "productInformations": "Product Informations", "optionsAndVariations": "Options and Variations", "optionDeleteError": "Error deleting the option", "optionDeletedSuccess": "Option deleted!", "noProducts": "No products found", "noVariations": "No variations found", "noCategories": "No categories found", "noOptions": "No options found", "productDeleteError": "Error while deleting the product", "productDeletedSuccess": "Product deleted!", "variationDeleteError": "Error while deleting the variation", "variationDeletedSuccess": "Variation deleted!", "categoryDeleteError": "Error while deleting the category", "categoryDeletedSuccess": "Category deleted!", "optionName": "Option Name", "editOptionTitle": "Edit Option", "optionUpdateError": "Error while updating the option", "optionUpdatedSuccess": "Option updated!", "addOptionTitle": "Add Option", "optionCreationError": "Error while creating a new modifier", "optionCreatedSuccess": "Modifier created!", "editCategoryTitle": "Edit Category", "categoryImageLabel": "Category Image (Maximum size of 1MB to 3.5MB)", "categoryNameLabel": "Category Name", "categoryUpdateError": "Error on updating category", "categoryUpdatedSuccess": "Category updated!", "addAddon": "Add addon", "addonDeleteError": "Error on deleting addon", "addonDeletedSuccess": "<PERSON><PERSON> deleted!", "addonTableHead": "<PERSON><PERSON>", "addon": "<PERSON><PERSON>", "addonName": "Addon name", "addonImage": "Addon Image (Maximum size of 1MB to 3.5MB)", "addonCreatedSuccess": "<PERSON><PERSON> created successfully!", "addonCreationError": "Error creating addon", "editAddon": "Edit addon", "storeName": "Store Name", "address": "Address", "phone": "Phone", "website": "Website", "storeLogo": "Store Logo (Maximum size of 1MB to 3.5MB)", "storeSetup": "Store Setup", "storeUpdatedSuccess": "Store updated successfully", "storeUpdateError": "Error updating store", "loginSuccess": "Login Successful", "loginError": "Incorrect username and password", "infoPrompt": "Enter the information you entered while registering.", "username": "Username", "password": "Password", "rememberMe": "Remember me", "forgetPassword": "Forget Password?", "loading": "Loading...", "signIn": "Sign In", "apply": "Apply", "orders": "Orders", "orderNumber": "Order Number", "status": "Status", "type": "Type", "createdAt": "Created At", "kioskName": "Kiosk Name", "DINE_IN": "<PERSON>e In", "TAKE_AWAY": "Take Away", "DELIVERY": "Delivery", "PENDING": "Pending", "PREPARING": "Preparing", "READY": "Ready", "COMPLETED": "Completed", "CANCELLED": "Cancelled", "branch": "Branch", "branchUpdatedSuccess": "Branch updated successfully!", "branchUpdateError": "Error updating branch.", "branchName": "Branch Name", "branchConfiguration": "Branch Configuration", "branchLogo": "Branch Logo (Maximum size of 1MB to 3.5MB)", "users": "Users", "user": "User", "statut": "Status", "roles": "Roles", "activate": "Activate", "deactivate": "Deactivate", "changeRole": "Change Role", "updateInfo": "Update Information", "activated": "Activated", "nonActivated": "Non Activated", "ROLE_USER": "User", "ROLE_MANAGER": "Manager", "ROLE_OWNER": "Owner", "ROLE_ADMIN": "Admin", "units": "Units", "unit": "Unit", "abbreviation": "Abbreviation", "editUnitTitle": "Edit Unit", "addUnitTitle": "Add Unit", "unitName": "Unit Name", "unitCreatedSuccess": "Unit created successfully", "unitCreationError": "Error creating unit", "unitUpdatedSuccess": "Unit updated successfully", "unitUpdateError": "Error updating unit", "unitDeleteError": "Error deleting unit", "unitDeletedSuccess": "Unit deleted successfully", "selectUnit": "--Select Unit--", "inventory": "Inventory", "inventoryTracked": "Tracked", "notTracked": "Not tracked", "isInventoryTracked": "Is inventory tracked?", "kiosks": "Kiosks", "noKiosks": "No kiosks available.", "changeBranch": "Change Branch", "kiosk": "Kiosk", "IN_SERVICE": "In Service", "CLEANING": "Cleaning", "DISABLED": "Disabled", "edit": "Edit", "language": "Language", "translatedName": "Translated Name", "translations": "Translations"}