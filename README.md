# QuickOrder Dashboard

This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

"use client";

import { useEffect, useState } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogClose } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import Image from "next/image";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { getCategories } from "@/config/category";
import { getProductAddons, getProductOptionsWithVariations, getProductsByCategory } from "@/config/product";
import { Icon } from "@iconify/react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

import { Check, ChevronsUpDown } from "lucide-react";
import Loading from "@/components/ui/loading";
import toast from "react-hot-toast";
import Link from "next/link";
import { getStatusColor, tables } from "../tables/table-list";
import { set } from "date-fns";
import { getCustomers } from "@/config/customer";

const basketColumns = [
{ id: 1, label: "Produit" },
{ id: 2, label: "Quantité" },
{ id: 3, label: "Prix" },
{ id: 4, label: "" },
];

const SelectCustomer = ({ customers, selectedCustomer, setSelectedCustomer }: any) => {
const [open, setOpen] = useState(false);

return (
<Popover open={open} onOpenChange={setOpen}>
<PopoverTrigger asChild>
<Button
          variant="outline"
          color="secondary"
          role="combobox"
          aria-expanded={open}
          className="hover:text-default-300 hover:bg-transparent hover:border-default-300 border-default-300 font-normal w-full justify-between border px-3 py-2"
        >
{selectedCustomer ? <span>{`${selectedCustomer.firstName} ${selectedCustomer.lastName}`}</span> : "Client Non-Inscrit"}
<ChevronsUpDown className="mx-2 h-4 w-4 shrink-0 opacity-50" />
</Button>
</PopoverTrigger>
<PopoverContent className="w-full p-0">
<Command className="w-full">
<CommandInput placeholder="Rechercher un client" />
<CommandEmpty>Aucun client trouvé.</CommandEmpty>
<CommandGroup className="max-h-[300px] overflow-y-auto">
<CommandItem
key={0}
onSelect={() => {
setSelectedCustomer(null);
setOpen(false);
}} >
<Check className={cn("mr-2 h-4 w-4", selectedCustomer === null ? "opacity-100" : "opacity-0")} />
Client Non-Inscrit
</CommandItem>
{customers.map((customer: any) => (
<CommandItem
key={customer.id}
onSelect={() => {
setSelectedCustomer(customer);
setOpen(false);
}} >
<Check className={cn("mr-2 h-4 w-4", selectedCustomer?.id === customer.id ? "opacity-100" : "opacity-0")} />
{`${customer.firstName} ${customer.lastName}`}
</CommandItem>
))}
</CommandGroup>
</Command>
</PopoverContent>
</Popover>
);
};

const BasketTable = ({ basketTableItems, onIncrement, onDecrement, onDelete }: any) => {
return (

<Table className="">
<TableHeader>
<TableRow>
{basketColumns.map((column, index) => (
<TableHead key={`basket-table-header-${index}`} className="text-xs h-8 p-0 text-default-600 ltr:last:text-right rtl:last:text-left">
{column.label}
</TableHead>
))}
</TableRow>
</TableHeader>
<TableBody className="[&_tr:last-child]:border-1 py-2 mt-0">
{basketTableItems.map((item: any, index: number) => (
<TableRow key={`basket-item-${index}`} className="py-2">
<TableCell className="text-xs font-medium text-default-600 max-w-[200px] truncate p-0 py-1">
<div className="text-xs text-card-foreground">{item.product.name}</div>
{/_ <div>
{item.variations.map((item: any, index: number) => (
<div key={`item-${index}`} className="text-xs font-light">
{item.option.name}
</div>
))}
</div> _/}
</TableCell>
<TableCell className="text-xs font-medium text-default-600 p-0 py-1 flex items-center gap-2">
<Button size="icon" variant="outline" className="h-6 w-6" color="primary" onClick={() => onDecrement(index)}>
<Icon icon="heroicons:minus" className="h-4 w-4" />
</Button>
<span>{item.qty}</span>
<Button size="icon" variant="outline" className="h-6 w-6" color="primary" onClick={() => onIncrement(index)}>
<Icon icon="heroicons:plus" className="h-4 w-4" />
</Button>
</TableCell>
<TableCell className="text-xs font-medium text-default-600 whitespace-nowrap p-0 py-1">{item.amount}€</TableCell>
<TableCell className="py-0">
<Button size="icon" variant="outline" className="h-6 w-6" color="destructive" onClick={() => onDelete(index)}>
<Icon icon="heroicons:trash" className="h-4 w-4" />
</Button>
</TableCell>
</TableRow>
))}
</TableBody>
</Table>
);
};

const POSPageView = ({ trans }: any) => {
const [categories, setCategories] = useState<any[]>([]);
const [customers, setCustomers] = useState<any[]>([]);
const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
const [selectedCategoryId, setSelectedCategoryId] = useState<any>(null);
const [products, setProducts] = useState<any[]>([]);
const [selectedProduct, setSelectedProduct] = useState<any>(null);
const [basketItems, setBasketItems] = useState<any[]>([]);
const [loading, setLoading] = useState(false);
const [isConfimModalOpen, setIsConfirmModalOpen] = useState(false);
const [selectTableOpen, setSelectTableOpen] = useState(false);
const [selectedOption, setSelectedOption] = useState(1);
const [selectedTable, setSelectedTable] = useState<any>(null);

// Function to add product to basket
const addToBasket = (product: any, quantity: number, selectedVariations: any, amount: string) => {
setBasketItems((prevItems) => [...prevItems, { product, qty: quantity, variations: selectedVariations, amount }]);
};

const calculateTotal = () => {
return basketItems.reduce((total, item) => total + parseFloat(item.amount), 0);
};
// Fetch categories on component mount
useEffect(() => {
const fetchCategories = async () => {
setLoading(true);
const categoriesData = await getCategories();
const customersData = await getCustomers();
setCustomers(customersData || []);
setCategories(categoriesData || []);
setSelectedCategoryId(categoriesData?.[0]?.id);

      setLoading(false);
    };
    fetchCategories();

}, []);

// Fetch products when a category is selected
useEffect(() => {
const fetchProducts = async () => {
setLoading(true);
if (selectedCategoryId) {
const productsData = await getProductsByCategory(selectedCategoryId);
setProducts(productsData || []);
}

      setLoading(false);
    };
    fetchProducts();

}, [selectedCategoryId]);

const handleCategorySelect = (id: any) => {
setSelectedCategoryId(id);
};

const handleProductClick = async (product: any) => {
const optionsAndVariations = await getProductOptionsWithVariations(product.id as any);

    console.log("Options with Variations:", optionsAndVariations);
    setSelectedProduct({ product, optionsAndVariations: optionsAndVariations || [] });

};

const closeModal = () => {
setSelectedProduct(null);
};

const handleIncrement = (index: number) => {
setBasketItems((prevItems) =>
prevItems.map((item, i) =>
i === index ? { ...item, qty: item.qty + 1, amount: (parseFloat(item.amount) + item.product.price).toFixed(2) } : item
)
);
};

const handleDecrement = (index: number) => {
setBasketItems(
(prevItems) =>
prevItems
.map((item, i) =>
i === index && item.qty > 1 ? { ...item, qty: item.qty - 1, amount: (parseFloat(item.amount) - item.product.price).toFixed(2) } : item
)
.filter((item) => item.qty > 0) // Remove items with qty of 0
);
};

const handleDelete = (index: number) => {
setBasketItems((prevItems) => prevItems.filter((\_, i) => i !== index));
toast.success("Produit retiré du panier.");
};

const clearBasket = () => {
setBasketItems([]); // Vider le panier
toast.success("Panier vidé.");
};

return (
<>
{loading && (

<div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
<Loading />
</div>
)}
<div className="grid grid-cols-4 gap-4 min-h-[100vh]  p-4">
{/_ Categories and Products Card _/}

        <div className="flex-col col-span-3 w-full h-full">
          {/* <div className="flex-none mb-4">
            <div className="text-lg font-semibold flex items-center">
              <Input placeholder="Search..." className="border-r-0 rounded-e-none" size="md" />
              <Button size="md" className="p-0 px-4 rounded-s-none border-l-0">
                <Icon icon="heroicons:magnifying-glass" className="h-4 w-4" />
              </Button>
            </div>
          </div> */}
          <div
            className="flex-grow  min-h-[620px] max-h-[620px] overflow-y-auto [&::-webkit-scrollbar]:w-[5px]
                      [&::-webkit-scrollbar-track]:rounded-full
                      [&::-webkit-scrollbar-track]:bg-gray-100
                      [&::-webkit-scrollbar-thumb]:rounded-full
                      [&::-webkit-scrollbar-thumb]:bg-primary-300
                      dark:[&::-webkit-scrollbar-track]:bg-neutral-700
                      dark:[&::-webkit-scrollbar-thumb]:bg-neutral-50"
          >
            <div
              className="flex space-x-4 overflow-x-auto [&::-webkit-scrollbar]:h-[0px]
                      [&::-webkit-scrollbar-track]:rounded-full
                      [&::-webkit-scrollbar-track]:bg-gray-100
                      [&::-webkit-scrollbar-thumb]:rounded-full
                      [&::-webkit-scrollbar-thumb]:bg-primary-300
                      dark:[&::-webkit-scrollbar-track]:bg-neutral-700
                      dark:[&::-webkit-scrollbar-thumb]:bg-neutral-50  pb-3 w-full"
            >
              {categories.map((category) => (
                <div
                  key={category.id}
                  onClick={() => handleCategorySelect(category.id)}
                  className={cn(
                    "min-w-[100px] h-full p-2 cursor-pointer rounded-xl bg-white dark:bg-neutral-800 shadow-none hover:bg-primary-50 hover:border-b-2 hover:border-b-primary-500",
                    {
                      "bg-primary-50 border-b-2 border-b-primary-500": selectedCategoryId === category.id,
                    }
                  )}
                >
                  <img src={category.imageUrl} alt={category.name} className="w-10 h-10 object-cover rounded mx-auto" />
                  <p className="mt-4 text-center font-medium">{category.name}</p>
                </div>
              ))}
            </div>

            {products.length > 0 ? (
              <div className="grid sm:grid-cols-2 md:grid-cols-5 lg:grid-cols-5 gap-4 mt-4">
                {products.map((product) => (
                  <div key={product.id} className="bg-white dark:bg-neutral-800 overflow-hidden p-2  rounded-xl">
                    <img src={product.imageUrl} alt={product.name} className=" w-32 h-32 object-cover rounded mx-auto p-0" />

                    <p className="text-md font-medium mt-2">{product.name}</p>
                    <div className="flex justify-between items-center ">
                      <p className="font-bold">{product.price}€</p>
                      <div
                        onClick={() => handleProductClick(product)}
                        className=" cursor-pointer text-xs text-primary shadow-sm  rounded-lg px-2 py-1 flex items-center gap-x-1
             transition-all duration-200 ease-in-out transform hover:bg-primary hover:text-white hover:scale-105"
                      >
                        <Icon icon="heroicons:shopping-bag" className="w-4 h-4" />
                        Ajouter
                      </div>
                    </div>

                    {/*  */}
                  </div>
                ))}
              </div>
            ) : (
              <div className="h-full flex flex-col justify-center mt-10">
                <div className="h-full flex justify-center items-center">
                  <div className="text-center flex flex-col items-center">
                    <Icon icon="heroicons:exclamation-circle" className="text-7xl text-default-300" />
                    <div className="mt-4 text-lg font-medium text-default-500">Aucun produit trouvé pour cette catégorie</div>
                  </div>
                </div>
              </div>
            )}

            {selectedProduct && (
              <ProductModal
                setLoading={setLoading}
                isOpen={!!selectedProduct}
                onClose={closeModal}
                productData={selectedProduct}
                addToBasket={addToBasket}
              />
            )}
          </div>
        </div>

        {/* Billing Section */}
        <Card className="p-4 shadow-none">
          <div className="mb-2">
            <div className="flex items-center gap-3">
              <SelectCustomer setSelectedCustomer={setSelectedCustomer} selectedCustomer={selectedCustomer} customers={customers} />
            </div>
          </div>
          <div className="flex flex-col text-sm h-[calc(100%-50px)]">
            <div
              className="max-h-[500px] overflow-x-auto [&::-webkit-scrollbar]:w-[5px]
                      [&::-webkit-scrollbar-track]:rounded-full
                      [&::-webkit-scrollbar-track]:bg-gray-100
                      [&::-webkit-scrollbar-thumb]:rounded-full
                      [&::-webkit-scrollbar-thumb]:bg-primary-300
                      dark:[&::-webkit-scrollbar-track]:bg-neutral-700
                      dark:[&::-webkit-scrollbar-thumb]:bg-neutral-50  pb-3 w-full"
            >
              {basketItems.length > 0 ? (
                <BasketTable basketTableItems={basketItems} onIncrement={handleIncrement} onDecrement={handleDecrement} onDelete={handleDelete} />
              ) : (
                <div className="flex flex-col items-center justify-center text-center w-full h-full text-gray-500 pt-8">
                  <Icon icon="heroicons:shopping-cart" className="text-5xl text-default-300" />
                  <p className="text-lg ">Aucun article dans le panier</p>
                </div>
              )}
            </div>

            {/* Discount form */}
            {/* {basketItems.length > 0 && (
              <div className="flex w-full items-center space-x-2">
                <Select defaultValue="fixed">
                  <SelectTrigger className="h-8 w-auto">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fixed">Fixed</SelectItem>
                    <SelectItem value="percentage">Percentage</SelectItem>
                  </SelectContent>
                </Select>
                <Input className="h-8 w-full" placeholder="Add Discount" type="number" />
                <Button variant="outline" color="success" className="h-8">
                  <Icon icon="heroicons:check" className="h-4 w-4" />
                </Button>
              </div>
            )} */}
            <div className="flex space-x-4 mb-4 mt-auto">
              <SelectTableModal
                setLoading={setLoading}
                isOpen={selectTableOpen}
                onClose={() => setSelectTableOpen(false)}
                selectedTable={selectedTable}
                setSelectedTable={setSelectedTable}
              />
              <button
                onClick={() => {
                  setSelectedOption(1);
                  setSelectedTable(null);
                }}
                className={cn(
                  "w-full p-2 cursor-pointer rounded-xl bg-white dark:bg-neutral-800 shadow-md hover:bg-primary-50 hover:border-b-2 hover:border-b-primary-500",
                  {
                    "bg-primary-50 border-b-2 border-b-primary-500": selectedOption === 1,
                  }
                )}
              >
                À emporter
              </button>
              <button
                onClick={() => {
                  setSelectTableOpen(true);
                  setSelectedOption(2);
                }}
                className={cn(
                  "flex flex-col items-center w-full p-2 cursor-pointer rounded-xl bg-white dark:bg-neutral-800 shadow-md hover:bg-primary-50 hover:border-b-2 hover:border-b-primary-500",
                  {
                    "bg-primary-50 border-b-2 border-b-primary-500": selectedOption === 2,
                  }
                )}
              >
                <span>Sur place</span> <span>{selectedTable && `Table ${selectedTable.id}`}</span>
              </button>
            </div>

            {/* Total Summary */}
            <div>
              <div className="flex mb-2 justify-between">
                <span>Remise Coupon :</span>
                <span>0€</span>
              </div>
              <div className="flex mb-2 justify-between">
                <span>TVA (%) :</span>
                <span>0€</span>
              </div>
              <div className="flex mb-2 justify-between">
                <span>Sous-total :</span>
                <span className="font-semibold"> {calculateTotal().toFixed(2)}€</span>
              </div>

              {/* Taxe et Total peuvent être ajoutés si nécessaire */}
            </div>

            <div className="flex items-center space-x-2 ">
              <ConfirmOrderModal
                setLoading={setLoading}
                isOpen={isConfimModalOpen}
                onClose={() => setIsConfirmModalOpen(false)}
                setBasketItems={setBasketItems}
                basketItems={basketItems}
                selectedTable={selectedTable}
                setSelectedTable={setSelectedTable}
                totalToPay={calculateTotal().toFixed(2)}
                selectedCustomer={selectedCustomer}
                setSelectedCustomer={setSelectedCustomer}
              />

              <Button disabled={basketItems.length === 0} className="w-full" color="destructive" onClick={clearBasket}>
                Annuler
              </Button>
              <Button disabled={basketItems.length === 0} className="w-full" color="success" onClick={() => setIsConfirmModalOpen(true)}>
                Commander
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </>

);
};

export const SelectTableModal = ({ isOpen, onClose, setLoading, setSelectedTable, selectedTable }: any) => {
return (

<Dialog open={isOpen} onOpenChange={onClose}>
<DialogContent onInteractOutside={(event) => event.preventDefault()} size="xl">
<DialogHeader>
<DialogTitle>Sélectionner une table</DialogTitle>
</DialogHeader>

        {/* Option Buttons */}
        <div className="flex flex-wrap gap-4">
          {tables.map((table) => (
            <div
              onClick={() => {
                setSelectedTable(table);
                onClose();
              }}
              key={table.id}
              className={`cursor-pointer w-24 h-24 border shadow-lg rounded-lg flex flex-col items-center justify-center
                ${selectedTable && selectedTable.id === table.id ? "bg-primary-100 border-1 border-gray-900" : ""}
                ${getStatusColor(table.status)}`}
            >
              <div
                className="w-12 h-12 flex items-center justify-center rounded-md bg-cover bg-center"
                style={{ backgroundImage: 'url("https://icon-library.com/images/dining-table-icon/dining-table-icon-13.jpg")' }}
              ></div>
              <span className="font-semibold text-xl text-black mb-1">{table.id}</span>
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>

);
};

export const ConfirmOrderModal = ({
isOpen,
onClose,
setLoading,
basketItems,
setBasketItems,
totalToPay,
selectedTable,
setSelectedTable,
selectedCustomer,
setSelectedCustomer,
}: any) => {
const [paymentAmount, setPaymentAmount] = useState("0.00");
const currentDate = new Date().toLocaleString("fr-FR", {
day: "2-digit",
month: "short",
year: "numeric",
hour: "2-digit",
minute: "2-digit",
});

const handleConfirm = () => {
// Clear the basket
setPaymentAmount("0.00");
setBasketItems([]);
setSelectedTable(null);
setSelectedCustomer(null);
// Show a success toast
toast.success("Commande confirmée!");

    // Close the modal
    onClose();

};

const handlePendingPayment = () => {
// Handle logic for pending payment
toast.success("Paiement en attente!"); // Show a toast notification for pending payment
setPaymentAmount("0.00");
setBasketItems([]);
setSelectedTable(null);
setSelectedCustomer(null);

    // Close the modal
    onClose();

};

const handleKeypadInput = (value: string) => {
if (value === "Cancel") {
setPaymentAmount("0.00");
} else if (value === "Backspace") {
setPaymentAmount((prev) => prev.slice(0, -1) || "0.00");
} else {
setPaymentAmount((prev) => (prev === "0.00" ? value : prev + value));
}
};

return (

<Dialog open={isOpen} onOpenChange={onClose}>
<DialogContent onInteractOutside={(event) => event.preventDefault()} size="4xl">
<div className="grid grid-cols-2 gap-4">
<div className="p-2">
<div className="pt-3 w-full flex justify-center">
<img src="https://stackfood-admin.6amtech.com/public/assets/admin/img/restaurant-invoice.png" className="w-12 h-12" alt="" />
</div>
<div className="text-center pt-3 mb-3">
<h5 className="font-bold text-lg">QuickOrder</h5>
<h5 className="text-break font-medium">123 Main Street, Downtown, Cityville</h5>
<h5>
<span>Téléphone</span> <span>:</span> <span>+****************</span>
</h5>
<h5 className="text-gray-500">{currentDate}</h5>
</div>
<h5 className="flex justify-between gap-2 mb-1">
<span>Type de Commande</span>
<span>{selectedTable ? `Sur place - Table ${selectedTable.id}` : "À emporter"}</span>
</h5>
<div className="border border-dashed border-gray-400 p-3 rounded">
<h5 className="flex justify-between gap-2">
<span className="text-gray-500">ID de Commande</span>
<span>100068</span>
</h5>
<h5 className="flex justify-between gap-2">
<span className="text-gray-500">Nom du Client</span>
<span>{selectedCustomer && isOpen ? ` ${selectedCustomer.firstName} ${selectedCustomer.lastName}` : " Non-Inscrit"}</span>
</h5>
<h5 className="flex justify-between gap-2">
<span className="text-gray-500">Téléphone</span>
<span>{selectedCustomer && isOpen ? ` ${selectedCustomer.phone}` : " Non-Inscrit"}</span>
</h5>
{/_ <h5 className="flex justify-between gap-2 text-break">
<span className="text-gray-500 text-nowrap">Adresse de Livraison</span>
<span className="text-right">Q972+VPF, Dhaka, Bangladesh</span>
</h5> _/}
</div>
<table className="table-auto w-full mt-4 mb-4">
<thead>
<tr className="border-b border-dashed border-gray-400">
<th className="text-left">QTE</th>
<th className="text-left">Article</th>
<th className="text-right">Prix</th>
</tr>
</thead>
<tbody>
{basketItems &&
basketItems.map((item: any) => (
<tr key={item.id}>
<td>x{item.qty}</td>
<td>
{item.product.name} <br />
<div className="text-sm text-gray-600">
<span>Prix : </span>
<span className="font-bold">{item.product.price} €</span>
</div>
</td>
<td className="text-right">{item.amount} €</td>
</tr>
))}
</tbody>
</table>
<div className="border-b border-dashed mb-3"></div>
<div>
<div className="px-3">
<dl className="text-right space-y-2">
{/_ <div className="flex justify-between">
<dt className="text-left text-gray-500">Prix des Articles</dt>
<dd>{totalToPay} €</dd>
</div>
<div className="flex justify-between">
<dt className="text-left text-gray-500">Coût des Suppléments</dt>
<dd>0,00 €</dd>
</div>
<div className="border-b border-dashed my-2"></div>_/}
<div className="flex justify-between">
<dt className="text-left font-medium">Prix des Articles</dt>
<dd className="font-medium">{totalToPay} €</dd>
</div>
<div className="border-b border-dashed my-2"></div>
<div className="flex justify-between">
<dt className="text-left text-gray-500">Remise</dt>
<dd>- 0,00 €</dd>
</div>
<div className="flex justify-between">
<dt className="text-left text-gray-500">Remise du Coupon</dt>
<dd>- 0,00 €</dd>
</div>
<div className="flex justify-between">
<dt className="text-left text-gray-500">TVA/Taxe</dt>
<dd>0,00 €</dd>
</div>
{/_ <div className="flex justify-between">
<dt className="text-left text-gray-500">Pourboire du Livreur</dt>
<dd>0,00 €</dd>
</div>
<div className="flex justify-between">
<dt className="text-left text-gray-500">Frais de Livraison</dt>
<dd>0,00 €</dd>
</div> _/}
<div className="flex justify-between">
<dt className="text-left text-gray-500">Frais de Service :</dt>
<dd>+ 0,00 €</dd>
</div>
<div className="border-b border-dashed my-2"></div>
<div className="flex justify-between text-xl font-medium">
<dt className="text-left">Total</dt>
<dd>{totalToPay} €</dd>
</div>
</dl>
</div>
</div>
{/_ <div className="border-b border-dashed my-2"></div>
<div className="flex justify-between">
<span className="capitalize">Payé par : Paiement Ssl commerz</span>
</div> _/}
<div className="border-b border-dashed my-2"></div>
<h5 className="text-center font-medium mb-2">MERCI</h5>
<div className="text-center">Pour avoir commandé sur QuickOrder</div>
<div className="border-b border-dashed my-2"></div>
{/_ <span className="block text-center">&copy; 2024 QuickOrder. Tous droits réservés</span> _/}
</div>

          <div className="mt-4 p-2">
            <div className="flex items-center justify-between bg-white shadow-sm rounded-lg p-3 mb-4">
              <div>
                <p className="text-sm text-gray-500">Montant à payer</p>
                <p className="text-2xl font-bold text-green-600">{totalToPay} €</p>
              </div>

              <div className="flex items-center space-x-4">
                <div>
                  <p className="text-sm text-gray-500">Crédit</p>
                  <p className="text-base font-semibold text-gray-800">{parseFloat(paymentAmount).toFixed(2)} €</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Solde</p>
                  <p className="text-base font-semibold text-gray-800">{(parseFloat(paymentAmount) - parseFloat(totalToPay)).toFixed(2)} €</p>
                </div>
              </div>
            </div>

            <Tabs defaultValue="cash" className="">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="cash">Espèces</TabsTrigger>
                <TabsTrigger value="other">Autres Modes</TabsTrigger>
              </TabsList>

              <TabsContent value="cash" className="mt-4">
                {/* Keypad UI */}
                <div className="px-1 mx-auto">
                  {/* Display */}
                  {/* <div className="flex items-center justify-end bg-white px-4 py-3 rounded-md text-xl font-bold border border-gray-300">
                    <span>{`${parseFloat(paymentAmount).toFixed(2)} €`}</span>
                  </div> */}
                  <input
                    type="text"
                    value={`${parseFloat(paymentAmount).toFixed(2)} €`}
                    readOnly
                    className="w-full p-3 text-right text-2xl border rounded-lg"
                  />

                  {/* Keypad */}
                  <div className="grid grid-cols-3 gap-2 mt-4">
                    {["1", "2", "3", "4", "5", "6", "7", "8", "9", "00", "0"].map((key) => (
                      <button
                        key={key}
                        className="p-4 text-xl font-medium bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                        onClick={() => handleKeypadInput(key)}
                      >
                        {key}
                      </button>
                    ))}
                    <button
                      className="p-4 text-xl font-medium bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                      onClick={() => handleKeypadInput("Backspace")}
                    >
                      <svg className="w-5 h-5 mx-auto" fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                    <button
                      className="p-4 text-xl font-medium bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                      onClick={() => handleKeypadInput(".")}
                    >
                      .
                    </button>
                    <button
                      className="col-span-2 p-4 text-xl font-medium bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                      onClick={() => handleKeypadInput("Cancel")}
                    >
                      Annuler
                    </button>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="other">
                {/* Other payment methods */}
                <p>Vous pouvez inclure ici d'autres options de modes de paiement.</p>
              </TabsContent>
            </Tabs>

            <div className="flex gap-2">
              <Button onClick={handleConfirm} className="w-full mt-4" color="success">
                Confirmer le paiement
              </Button>
              <Button onClick={handlePendingPayment} className="w-full mt-4" color="warning">
                Paiement en attente
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>

);
};

export const ProductModal = ({ isOpen, onClose, productData, addToBasket, setLoading }: any) => {
const [quantity, setQuantity] = useState(1);
const [selectedVariations, setSelectedVariations] = useState<any>([]);
const { product, optionsAndVariations } = productData;

// Handle quantity change
const handleQuantityChange = (type: any) => {
setQuantity(type === "increment" ? quantity + 1 : Math.max(1, quantity - 1));
};

// Handle variation selection with maxAllowed logic
const handleVariationSelect = (option: any, variation: any, maxAllowed: number = 1) => {
setSelectedVariations((prevSelected: any) => {
const optionIndex = prevSelected.findIndex((item: any) => item.option.id === option.id);

      // If the option is already selected
      if (optionIndex !== -1) {
        const currentVariations = prevSelected[optionIndex].variations;

        // If variation is already selected, deselect it
        if (currentVariations.some((v: any) => v.id === variation.id)) {
          const updatedVariations = currentVariations.filter((v: any) => v.id !== variation.id);

          // Update the selected option with new variations, or remove option if no variations left
          if (updatedVariations.length === 0) {
            return prevSelected.filter((item: any) => item.option.id !== option.id);
          }

          return [...prevSelected.slice(0, optionIndex), { option, variations: updatedVariations }, ...prevSelected.slice(optionIndex + 1)];
        }

        // If variation is not selected and within maxAllowed, add it
        if (currentVariations.length < maxAllowed) {
          return [
            ...prevSelected.slice(0, optionIndex),
            { option, variations: [...currentVariations, variation] },
            ...prevSelected.slice(optionIndex + 1),
          ];
        }

        // If maxAllowed reached, replace the last selected variation
        return [
          ...prevSelected.slice(0, optionIndex),
          { option, variations: [...currentVariations.slice(1), variation] },
          ...prevSelected.slice(optionIndex + 1),
        ];
      }

      // If option not selected, add it with the new variation
      return [...prevSelected, { option, variations: [variation] }];
    });

};

// Calculate total price based on selected variations
const calculateTotalPrice = () => {
const basePrice = product.price \* quantity;

    const variationsPrice = selectedVariations
      .flatMap((item: any) => item.variations) // Flatten all selected variations
      .reduce((total: number, variation: any) => total + variation.price, 0); // Sum up the prices of selected variations

    return basePrice + variationsPrice * quantity;

};

const handleAddToCart = () => {
addToBasket(product, quantity, selectedVariations, calculateTotalPrice().toFixed(2));
onClose();
toast.success("Produit ajouté au panier");
};

return (

<Dialog open={isOpen} onOpenChange={onClose}>
<DialogContent
onInteractOutside={(event) => event.preventDefault()}
size="xl"
className="gap-1 space-y-0 max-h-[750px] overflow-x-auto [&::-webkit-scrollbar]:w-[5px]
[&::-webkit-scrollbar-track]:rounded-full
[&::-webkit-scrollbar-track]:bg-gray-100
[&::-webkit-scrollbar-thumb]:rounded-full
[&::-webkit-scrollbar-thumb]:bg-primary-300
dark:[&::-webkit-scrollbar-track]:bg-neutral-700
dark:[&::-webkit-scrollbar-thumb]:bg-neutral-50" >
<div className="flex">
<img src={product.imageUrl} alt={product.name} className=" w-[100px] h-[100px] object-cover rounded" />
<div className="space-y-1 ml-7 flex flex-col justify-center">
<div className="text-lg font-semibold">{product.name}</div>
<div className="text-sm text-gray-500 ">{product.description}</div>
<div className="text-lg font-semibold ">{product.price}€</div>
</div>
</div>

        <div className="flex items-center space-x-4">
          <label className="block font-medium">Quantité : </label>
          <div className="text-sm font-medium text-default-600 py-2 flex items-center gap-2">
            <Button size="icon" variant="outline" className="h-8 w-8" color="primary" onClick={() => handleQuantityChange("decrement")}>
              <Icon icon="heroicons:minus" className="h-4 w-4" />
            </Button>
            <span>{quantity}</span>
            <Button size="icon" variant="outline" className="h-8 w-8" color="primary" onClick={() => handleQuantityChange("increment")}>
              <Icon icon="heroicons:plus" className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Options and variations */}
        {optionsAndVariations.map((item: any) => (
          <div key={item.option.id} className="flex flex-col space-y-2 pb-2">
            <label className="block font-medium">{item.option.name}</label>
            <div className="flex space-x-4">
              {item.variations.map((variation: any) => (
                <div
                  key={variation.id}
                  className={`flex items-center space-x-2 p-2 pr-4 border rounded-lg cursor-pointer ${
                    selectedVariations.some(
                      (selected: any) => selected.option.id === item.option.id && selected.variations.some((v: any) => v.id === variation.id)
                    )
                      ? "border-primary-500 text-primary-500 bg-primary-100"
                      : ""
                  }`}
                  onClick={() => handleVariationSelect(item.option, variation, item.maxAllowed)}
                >
                  <img src={variation.imageUrl} alt={variation.name} className="w-[40px] h-[40px] object-cover rounded" />
                  <div>
                    <div>{variation.name}</div>
                    <div className="font-semibold">+ {variation.price}€</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* Special Instructions */}
        <div className="my-4">
          <label className="block font-medium mb-2">Instructions spéciales</label>
          <Textarea placeholder="Ajouter une note (mayo supplémentaire, fromage, etc.)" />
        </div>

        {/* Add to Cart Button */}
        <Button onClick={handleAddToCart} className="w-full mt-1">
          Ajouter au panier <span className="font-semibold ml-3"> {calculateTotalPrice().toFixed(2)}€ </span>
        </Button>
      </DialogContent>
    </Dialog>

);
};

export default POSPageView;
