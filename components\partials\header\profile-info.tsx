"use client";
import { useSession } from "next-auth/react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Icon } from "@iconify/react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import avatar5 from "@/public/images/avatar/avatar-5.jpg";
import { useAuthStore } from "@/store";
import { updatePosSession } from "@/services/pos-session";

const ProfileInfo = () => {
  const { data: session } = useSession();
  const { posSession, setPosSession } = useAuthStore();
  const router = useRouter();

  const handlePauseSession = async () => {
    const sessionBody = {
      closedAt: new Date(),
      status: "CLOSED",
    };

    const openPosSession = await updatePosSession(posSession.id, sessionBody);

    setPosSession({ ...openPosSession, redirectedFrom: "pos" });

    router.push("/session");
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild className=" cursor-pointer">
        <div className=" flex items-center  ">
          <Image src={avatar5} alt="" width={36} height={36} className="rounded-full" />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56 p-0" align="end">
        <DropdownMenuLabel className="flex gap-2 items-center mb-1 p-3">
          <Image src={avatar5} alt="" width={36} height={36} className="rounded-full" />
          <div>
            <div className="text-sm font-medium text-default-800 capitalize ">
              {session?.user?.firstName} {session?.user?.lastName}
            </div>
            <Link href="/dashboard" className="text-xs text-default-600 hover:text-primary">
              {session?.user?.email}
            </Link>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator className="mb-0 dark:bg-background" />
        <DropdownMenuItem
          onSelect={handlePauseSession}
          className="flex items-center gap-2 text-sm font-medium text-default-600 capitalize my-1 px-3 dark:hover:bg-background cursor-pointer"
        >
          <Icon icon="heroicons:pause" className="w-4 h-4" />
          Pause Session
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ProfileInfo;
