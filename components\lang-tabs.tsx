"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { getLanguages } from "@/services/language";
import { ILanguage } from "@/lib/interface";
import { Icon } from "@iconify/react";
import { TabsContent } from "@radix-ui/react-tabs";
import { useEffect, useState } from "react";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Switch } from "./ui/switch";
import { cn } from "@/lib/utils";

const LangTabs = ({ labelValue = "Name", translations, setTranslations, trans, defaultLanguage, setDisplayedLangId, error }: any) => {
  const [languages, setLanguages] = useState<ILanguage[] | null>([
    {
      id: 1,
      code: "EN",
      name: "English",
    },
    {
      id: 2,
      code: "FR",
      name: "Français",
    },

    {
      id: 7,
      code: "AR",
      name: "Arabic",
    },
  ]);

  const [defaultLangId, setDefaultLangId] = useState<number>(1);

  useEffect(() => {
    const fetchData = async () => {
      try {
        //let languagesData = await getLanguages();
        setLanguages(languages || []);
        if (languages && languages.length > 0) {
          setTranslations(
            languages.map((lang) => ({
              languageId: lang.id,
              translatedName: null, // Default empty value
              isDefault: lang.id === languages[0].id,
            }))
          );
        }
      } catch (error) {
        console.error("Error fetching languages", error);
      }
    };

    fetchData();
  }, []);

  const handleInputChange = (langId: number, value: string) => {
    setTranslations((prev: any) => prev.map((trans: any) => (trans.languageId === langId ? { ...trans, translatedName: value } : trans)));
  };

  return (
    <Tabs defaultValue="English" className=" ">
      <TabsList className="bg-transparent p-0 rounded-none">
        {languages &&
          languages.map((lang) => (
            <TabsTrigger
              key={lang.id}
              className={cn(
                "capitalize data-[state=active]:shadow-none data-[state=active]:bg-transparent data-[state=active]:text-primary transition duration-150 before:transition-all before:duration-150 relative before:absolute before:left-1/2 before:-bottom-[5px] before:h-[2px] before:-translate-x-1/2 before:w-0 data-[state=active]:before:bg-primary data-[state=active]:before:w-full text-base",
                {
                  "text-red-500": error && lang.id == defaultLangId, // Make English text red if there's an error
                }
              )}
              value={lang.name}
              onClick={() => setDisplayedLangId(lang.id)}
            >
              {lang.name}
            </TabsTrigger>
          ))}
      </TabsList>
      {languages &&
        languages.map(
          (lang) =>
            defaultLangId !== lang.id && (
              <TabsContent key={lang.id} value={lang.name} className="flex items-center gap-4">
                <div className="flex flex-col gap-2 mt-4 w-full">
                  <Label htmlFor={`name-${lang.id}`}>
                    {labelValue} ({lang.name})
                  </Label>
                  <Input
                    id={`name-${lang.id}`}
                    type="text"
                    size="xl"
                    value={translations.find((trans: any) => trans.languageId === lang.id)?.translatedName || ""}
                    onChange={(e) => handleInputChange(lang.id, e.target.value)}
                  />
                </div>
              </TabsContent>
            )
        )}
    </Tabs>
  );
};

export default LangTabs;

// [
//   {
//     languageId: lang.id,
//     name: inputvalue,
//     isDefault: true,

//   }
// ]
