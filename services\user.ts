import { api } from "@/config/axios.config";
import { IUser } from "@/lib/interface";

// Function to get the list of users
export const getUsers = async (): Promise<IUser[] | null> => {
  try {
    const response = await api.get<IUser[]>("/users");
    return response.data;
  } catch (error) {
    console.error("Get Users error:", error);
    return null;
  }
};

// Function to get the list of users with pagination
interface IUserResponse {
  totalCount: number;
  data: IUser[];
}

export const getUsersWithPagination = async (
  page: number = 0,
  size: number = 10,
  sortBy: string = "username", // Assuming sorting by username by default
  sortDirection: "asc" | "desc" = "asc"
): Promise<IUserResponse | null> => {
  try {
    const response = await api.get<IUser[]>(`/users?size=${size}&page=${page}&sort=${sortBy},${sortDirection}`);

    // Extract total count from headers and the data from the response
    const totalCount = parseInt(response.headers["x-total-count"]);
    const data: IUser[] = response.data;

    return { totalCount, data };
  } catch (error) {
    console.error("Get Users with Pagination error:", error);
    return null;
  }
};

// Function to get the user by Id
export const getUserById = async (userId: number): Promise<IUser | null> => {
  try {
    const response = await api.get<IUser>(`/users/${userId}`);
    return response.data;
  } catch (error) {
    console.error("Get User by ID error:", error);
    return null;
  }
};
