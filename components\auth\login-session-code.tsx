"use client";
import { Input } from "@/components/ui/input";
import { createPosSession, updatePosSession } from "@/services/pos-session";
import { useAuthStore } from "@/store";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import { ChangeEvent, KeyboardEvent, useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";

const LoginSessionCodeForm = ({ inputType = "text", loadedFrom = "login" }: any) => {
  const totalOtpField = 4;
  const otpArray: string[] = Array.from({ length: totalOtpField }, () => "");
  const [otp, setOtp] = useState<string[]>(otpArray);
  const [loading, setLoading] = useState(false);
  const [blocked, setBlocked] = useState(false);
  const otpFields = Array.from({ length: totalOtpField }, (_, index) => index);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const router = useRouter();
  const { setPosSession, posSession } = useAuthStore();

  useEffect(() => {
    if (otp.filter((key) => key != "").length >= totalOtpField) {
      handleSubmit();
    }
  }, [otp]);

  const handleChange = (e: ChangeEvent<HTMLInputElement>, index: number) => {
    const { value } = e.target;
    if (!isNaN(Number(value)) && value.length <= 1) {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);
      if (value.length === 1 && index < totalOtpField - 1) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyDown = (index: number, event: KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Backspace" && otp[index] === "" && index > 0) {
      setOtp((prevOtp) => {
        const newOtp = [...prevOtp];
        newOtp[index - 1] = "";
        return newOtp;
      });
      inputRefs.current[index - 1]?.focus();
    } else if (event.key === "ArrowLeft" && index > 0) {
      inputRefs.current[index - 1]?.focus();
    } else if (event.key === "ArrowRight" && index < totalOtpField - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleButtonClick = (key: any) => {
    let activeIndex = otp.findIndex((digit) => digit == "");

    if (activeIndex == -1 && otp.length >= totalOtpField) {
      activeIndex = otp.length;
    }

    if (key == "←") {
      // Handle backspace logic
      if (activeIndex > 0) {
        setOtp((prevOtp) => {
          const newOtp = [...prevOtp];
          newOtp[activeIndex - 1] = "";
          return newOtp;
        });
        inputRefs.current[activeIndex - 1]?.focus();
      }
    } else if (key === "✓") {
      // Handle submit logic
      handleSubmit();
    } else if (!isNaN(Number(key))) {
      // Handle numeric input
      if (activeIndex !== -1 && activeIndex < otp.length) {
        const newOtp = [...otp];
        newOtp[activeIndex] = key.toString();
        setOtp(newOtp);

        // Move focus to the next input field
        if (activeIndex < otp.length - 1) {
          inputRefs.current[activeIndex + 1]?.focus();
        }
      }
    }
  };

  const handleSubmit = async () => {
    if (blocked) {
      toast.error("Trop de tentatives. Veuillez réessayer dans 1 minute.", { position: "bottom-center" });
      return;
    }

    const enteredOtp = otp.join("");

    setLoading(true);

    let response = await signIn("credentials", {
      code: enteredOtp,
      redirect: false,
    });

    if (response && response.ok) {
      toast.success("Connexion réussie", { position: "bottom-center" });

      const sessionBody = {
        openAt: new Date(),
        status: "ACTIVE",
      };

      let openPosSession: any = null;

      if (loadedFrom == "session") {
        openPosSession = await updatePosSession(posSession.id, sessionBody);
      } else {
        openPosSession = await createPosSession(sessionBody);
      }

      //const openPosSession = await createPosSession(sessionBody);

      setPosSession(openPosSession);

      router.push("/pos");
      //router.replace("/pos");
    } else {
      toast.error("Code d'accès incorrect", { position: "bottom-center" });
    }
    setLoading(false);

    console.log("Entered OTP:", enteredOtp);
    setOtp(otpArray);
    inputRefs.current[0]?.focus();
  };

  return (
    <div className="w-full h-full">
      <form className="w-full">
        <div className="flex  gap-1 lg:gap-2 items-center justify-between w-full">
          {otpFields.map((index) => (
            <Input
              key={`otp-code-${index}`}
              type={inputType}
              id={`otp${index}`}
              name={`otp${index}`}
              value={otp[index]}
              disabled={loading || blocked}
              onChange={(e) => handleChange(e, index)}
              onKeyDown={(event) => handleKeyDown(index, event)}
              maxLength={1}
              className="w-10 h-10 mx-auto sm:w-[60px] sm:h-16 rounded border-default-300 text-center text-2xl font-medium text-default-900"
              ref={(ref) => {
                inputRefs.current[index] = ref;
              }}
              readOnly
            />
          ))}
        </div>
        <div className="grid grid-cols-3 gap-4 mt-6">
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, "←", 0, "✓"].map((key, index) => (
            <button
              disabled={loading || blocked}
              key={index}
              type="button"
              onClick={() => handleButtonClick(key)}
              className="p-3 text-xl font-medium bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              {key}
            </button>
          ))}
        </div>
      </form>
    </div>
  );
};

export default LoginSessionCodeForm;
