import { ShoppingBag, LineChart, Computer, LayoutGrid, Users } from "lucide-react";

export interface MenuItemProps {
  title: string;
  icon: any;
  href?: string;
  child?: MenuItemProps[];
  megaMenu?: MenuItemProps[];
  multi_menu?: MenuItemProps[];
  nested?: MenuItemProps[];
  onClick: () => void;
}

export const ownerAndAdminMenusConfig = {
  mainNav: [
    {
      title: "POS",
      icon: Computer,
      href: "/pos",
    },
    {
      title: "orders",
      icon: ShoppingBag,
      href: "/orders",
    },
    {
      title: "Tables",
      icon: LayoutGrid,
      href: "/tables",
    },
    {
      //title: "Clients",
      title: "Clients",
      icon: Users,
      href: "/customers",
    },
    // {
    //   title: "Rapports",
    //   icon: LineChart,
    //   href: "/session-stats",
    // },
  ],
  sidebarNav: {
    modern: [
      {
        title: "POS",
        icon: Computer,
        href: "/pos",
      },

      {
        title: "orders",
        icon: ShoppingBag,
        href: "/orders",
      },
      {
        title: "Tables",
        icon: LayoutGrid,
        href: "/tables",
      },
      {
        title: "Clients",
        icon: Users,
        href: "/customers",
      },
      // {
      //   title: "Rapports",
      //   icon: LineChart,
      //   href: "/session-stats",
      // },
    ],
    classic: [
      {
        title: "POS",
        icon: Computer,
        href: "/pos",
      },

      {
        title: "orders",
        icon: ShoppingBag,
        href: "/orders",
      },
      {
        title: "Tables",
        icon: LayoutGrid,
        href: "/tables",
      },
      {
        title: "Clients",
        icon: Users,
        href: "/customers",
      },
    ],
  },
};

export const userMenusConfig = {
  mainNav: [
    {
      title: "POS",
      icon: Computer,
      href: "/pos",
    },
    {
      title: "orders",
      icon: ShoppingBag,
      href: "/orders",
    },
    {
      title: "Tables",
      icon: LayoutGrid,
      href: "/tables",
    },
    {
      title: "Clients",
      icon: Users,
      href: "/customers",
    },
    // {
    //   title: "Rapports",
    //   icon: LineChart,
    //   href: "/session-stats",
    // },
  ],
  sidebarNav: {
    modern: [
      {
        title: "POS",
        icon: Computer,
        href: "/pos",
      },

      {
        title: "orders",
        icon: ShoppingBag,
        href: "/orders",
      },
      {
        title: "Tables",
        icon: LayoutGrid,
        href: "/tables",
      },
      {
        title: "Clients",
        icon: Users,
        href: "/customers",
      },
      // {
      //   title: "Rapports",
      //   icon: LineChart,
      //   href: "/session-stats",
      // },
    ],
    classic: [
      {
        title: "POS",
        icon: Computer,
        href: "/pos",
      },

      {
        title: "orders",
        icon: ShoppingBag,
        href: "/orders",
      },
      {
        title: "Tables",
        icon: LayoutGrid,
        href: "/tables",
      },
      {
        title: "Clients",
        icon: Users,
        href: "/customers",
      },
      // {
      //   title: "Rapports",
      //   icon: LineChart,
      //   href: "/session-stats",
      // },
    ],
  },
};

export type UserModernNavType = (typeof userMenusConfig.sidebarNav.modern)[number];
export type UserClassicNavType = (typeof userMenusConfig.sidebarNav.classic)[number];
export type UserMainNavType = (typeof userMenusConfig.mainNav)[number];

export type OwnerAndAdminModernNavType = (typeof ownerAndAdminMenusConfig.sidebarNav.modern)[number];
export type OwnerAndAdminClassicNavType = (typeof ownerAndAdminMenusConfig.sidebarNav.classic)[number];
export type OwnerAndAdminMainNavType = (typeof ownerAndAdminMenusConfig.mainNav)[number];
