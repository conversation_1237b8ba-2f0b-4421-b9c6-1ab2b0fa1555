import { api } from "@/config/axios.config";

// Function to get the list of taxes
export const getTaxes = async (): Promise<any[] | null> => {
  try {
    const response = await api.get<any[]>("/tax");
    return response.data;
  } catch (error) {
    console.error("Get Taxs error:", error);
    return null;
  }
};

// Function to get a tax by ID
export const getTaxById = async (taxId: number): Promise<any | null> => {
  try {
    const response = await api.get<any>(`/tax/${taxId}`);
    return response.data;
  } catch (error) {
    console.error("Get Tax by ID error:", error);
    return null;
  }
};

// Function to create a new tax
export const createTax = async (newTax: any): Promise<any | null> => {
  try {
    const response = await api.post<any>("/tax", newTax);
    return response.data;
  } catch (error) {
    console.error("Create Tax error:", error);
    return null;
  }
};

// Function to update an existing tax by ID
export const updateTax = async (taxId: number, updatedTax: Partial<any>): Promise<any | null> => {
  try {
    const response = await api.patch<any>(`/tax/${taxId}`, updatedTax);
    return response.data;
  } catch (error) {
    console.error("Update Tax error:", error);
    return null;
  }
};

// Function to delete a tax by ID
export const deleteTax = async (taxId: number): Promise<boolean> => {
  try {
    await api.delete(`/tax/${taxId}`);
    return true;
  } catch (error) {
    console.error("Delete Tax error:", error);
    return false;
  }
};

// Function to get the branch for a tax by tax ID
export const getTaxBranch = async (taxId: number): Promise<any | null> => {
  try {
    const response = await api.get<any>(`/tax/${taxId}/branch`);
    return response.data;
  } catch (error) {
    console.error("Get Tax Branch error:", error);
    return null;
  }
};

// Function to delete the branch of a tax by tax ID
export const deleteTaxBranch = async (taxId: number): Promise<boolean> => {
  try {
    await api.delete(`/tax/${taxId}/branch`);
    return true;
  } catch (error) {
    console.error("Delete Tax Branch error:", error);
    return false;
  }
};
