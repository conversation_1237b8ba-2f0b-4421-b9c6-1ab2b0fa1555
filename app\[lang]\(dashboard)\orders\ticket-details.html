<div class="p-4">
  <div class="pt-3">
    <img
      src="https://stackfood-admin.6amtech.com/public/assets/admin/img/restaurant-invoice.png"
      class="w-full h-auto"
      alt=""
    />
  </div>
  <div class="text-center pt-3 mb-3">
    <h5 class="font-bold text-lg">Hungry Puppets</h5>
    <h5 class="text-break font-medium">House: 00, Road: 00, Test City</h5>
    <h5 class="text-gray-500">11/Jan/2022 01:53 pm</h5>
    <h5><span>Phone</span> <span>:</span> <span>+1**********</span></h5>
  </div>
  <h5 class="flex justify-between gap-2">
    <span>Order Type</span>
    <span>Home Delivery</span>
  </h5>
  <div class="border border-dashed border-gray-400 p-3 rounded">
    <h5 class="flex justify-between gap-2">
      <span class="text-gray-500">Order ID</span>
      <span>100068</span>
    </h5>
    <h5 class="flex justify-between gap-2">
      <span class="text-gray-500">Customer Name</span>
      <span>Ashek Elahe</span>
    </h5>
    <h5 class="flex justify-between gap-2">
      <span class="text-gray-500">Phone</span>
      <span>+8**********</span>
    </h5>
    <h5 class="flex justify-between gap-2 text-break">
      <span class="text-gray-500 text-nowrap">Delivery Address</span>
      <span class="text-right">Q972+VPF, Dhaka, Bangladesh</span>
    </h5>
  </div>
  <table class="table-auto w-full mt-4 mb-4">
    <thead>
      <tr class="border-b border-dashed border-gray-400">
        <th class="text-left">QTY</th>
        <th class="text-left">Item</th>
        <th class="text-right">Price</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>1x</td>
        <td>
          Medu Vada <br />
          <div class="text-sm text-gray-600">
            <span>Price : </span>
            <span class="font-bold">$ 95.00</span>
          </div>
        </td>
        <td class="text-right">$ 95.00</td>
      </tr>
    </tbody>
  </table>
  <div class="border-b border-dashed mb-3"></div>
  <div>
    <div class="px-3">
      <dl class="text-right space-y-2">
        <div class="flex justify-between">
          <dt class="text-left text-gray-500">Items Price</dt>
          <dd>$ 95.00</dd>
        </div>
        <div class="flex justify-between">
          <dt class="text-left text-gray-500">Addon Cost</dt>
          <dd>$ 0.00</dd>
        </div>
        <div class="border-b border-dashed my-2"></div>
        <div class="flex justify-between">
          <dt class="text-left font-medium">Subtotal</dt>
          <dd class="font-medium">$ 95.00</dd>
        </div>
        <div class="border-b border-dashed my-2"></div>
        <div class="flex justify-between">
          <dt class="text-left text-gray-500">Discount</dt>
          <dd>- $ 0.00</dd>
        </div>
        <div class="flex justify-between">
          <dt class="text-left text-gray-500">Coupon discount</dt>
          <dd>- $ 0.00</dd>
        </div>
        <div class="flex justify-between">
          <dt class="text-left text-gray-500">Vat/tax</dt>
          <dd>$ 4.75</dd>
        </div>
        <div class="flex justify-between">
          <dt class="text-left text-gray-500">Delivery man tips</dt>
          <dd>$ 0.00</dd>
        </div>
        <div class="flex justify-between">
          <dt class="text-left text-gray-500">Delivery charge</dt>
          <dd>$ 0.00</dd>
        </div>
        <div class="flex justify-between">
          <dt class="text-left text-gray-500">Service Charge:</dt>
          <dd>+ $ 0.00</dd>
        </div>
        <div class="border-b border-dashed my-2"></div>
        <div class="flex justify-between text-xl font-medium">
          <dt class="text-left">Total</dt>
          <dd>$ 99.75</dd>
        </div>
      </dl>
    </div>
  </div>
  <div class="border-b border-dashed my-2"></div>
  <div class="flex justify-between">
    <span class="capitalize">Paid by: Ssl commerz payment</span>
  </div>
  <div class="border-b border-dashed my-2"></div>
  <h5 class="text-center font-medium mb-2">THANK YOU</h5>
  <div class="text-center">For ordering food from StackFood</div>
  <div class="border-b border-dashed my-2"></div>
  <span class="block text-center"
    >&copy; 2024 StackFood. All rights reserved</span
  >
</div>
