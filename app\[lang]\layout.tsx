import "../assets/scss/globals.scss";
import "../assets/scss/theme.scss";
import { Inter } from "next/font/google";
import { siteConfig } from "@/config/site";
import Providers from "@/provider/providers";
import "simplebar-react/dist/simplebar.min.css";
import TanstackProvider from "@/provider/providers.client";
import AuthProvider from "@/provider/auth.provider";
import "flatpickr/dist/themes/light.css";
import DirectionProvider from "@/provider/direction.provider";
const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: {
    default: `${siteConfig.name} | POS`,
  },
  icons: {
    icon: "/favicon.ico",
  },
  description: siteConfig.description,
};

export default function RootLayout({ children, params: { lang } }: { children: React.ReactNode; params: { lang: string } }) {
  return (
    <html lang={lang}>
      <TanstackProvider>
        <Providers>
          <AuthProvider>
            <DirectionProvider lang={lang}>{children}</DirectionProvider>
          </AuthProvider>
        </Providers>
      </TanstackProvider>
    </html>
  );
}
