"use client";

import { getStatusColor } from "@/app/[lang]/(dashboard)/tables/table-list";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { createOrderWithDetails } from "@/services/order";
import { getTables, updateTable } from "@/services/table";
import { Icon } from "@iconify/react";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { SelectCustomer } from "./select-customer";

const generateOrderBody = (selectedType: number, selectedCustomer: any, selectedTable: any, basketItems: any[], paymentAmount: string) => {
  console.log(basketItems);
  const orderBody = {
    order: {
      type: selectedType === 1 ? "TAKE_AWAY" : "DINE_IN",
      customer_id: selectedCustomer ? selectedCustomer.id.toString() : null,
      table_id: selectedTable ? selectedTable.id.toString() : null,
      branch_id: 1,
      pos_id: 1,
    },

    orderDetails: basketItems.map((item: any) => ({
      productId: item.product.id.toString(),
      quantity: item.qty,
      specialInstructions: item.specialInstructions,
      variations: item.variations.flatMap((v: any) => v.variations.map((variationObj: any) => variationObj.variation.id.toString())) || [],
    })),
    payment: {
      amount: parseFloat(paymentAmount),
      paymentMethodId: "9",
    },
  };

  return orderBody;
};

export const ConfirmOrderModal = ({
  isOpen,
  onClose,
  setLoading,
  basketItems,
  setBasketItems,
  totalToPay,
  taxes,
  calculateTax,
  selectedTable,
  setSelectedTable,
  selectedCustomer,
  setSelectedCustomer,
  selectedType = 1,
  setSelectedType,
  customers,
  discountAmount,
  validCoupon,
}: any) => {
  const [paymentAmount, setPaymentAmount] = useState("0");
  const [hasDecimal, setHasDecimal] = useState(false);
  const [decimalCount, setDecimalCount] = useState(0);

  const formatAmount = (value: string): string => {
    const numericValue = Number.parseFloat(value);
    if (isNaN(numericValue)) return "0.00";

    if (value.includes(".")) {
      const [integer, decimal] = value.split(".");
      return `${integer}.${(decimal || "").padEnd(2, "0")}`;
    }

    return `${value}.00`;
  };

  const handleNumber = (num: string) => {
    setPaymentAmount((prev) => {
      // Handle initial state
      if (prev === "0" && num !== "00") return num;

      // Handle decimal cases
      if (hasDecimal) {
        if (decimalCount < 2) {
          setDecimalCount((prev) => prev + 1);
          return `${prev}${num}`;
        }
        return prev;
      }

      // Handle "00" special case
      if (num === "00" && prev === "0") return "0";

      return `${prev}${num}`;
    });
  };

  const handleDecimal = () => {
    if (!hasDecimal) {
      setHasDecimal(true);
      setPaymentAmount((prev) => `${prev}.`);
    }
  };

  const handleDelete = () => {
    setPaymentAmount((prev) => {
      if (prev.length === 1) return "0";

      // Handle decimal deletion
      if (prev.endsWith(".")) {
        setHasDecimal(false);
        setDecimalCount(0);
      } else if (hasDecimal) {
        setDecimalCount((prev) => Math.max(0, prev - 1));
      }

      return prev.slice(0, -1);
    });
  };

  const handleReset = () => {
    setPaymentAmount("0");
    setHasDecimal(false);
    setDecimalCount(0);
    setBasketItems([]);
    setSelectedTable(null);
    setSelectedCustomer(null);
    setSelectedType(1);
  };

  const handleCancel = () => {
    // Add your cancellation logic here
    console.log("Operation cancelled");
    handleReset();
  };
  const handleConfirm = async () => {
    const orderBody = generateOrderBody(selectedType, selectedCustomer, selectedTable, basketItems, paymentAmount);
    const createdOrder = await createOrderWithDetails(orderBody);

    if (createdOrder) {
      if (selectedTable && selectedType == 2) {
        await updateTable(selectedTable.id, { status: "OCCUPIED" });
      }
      toast.success(`Commande #${createdOrder?.order?.orderNumber} confirmée!`, { position: "bottom-center" });
      // const ticketContent = await generateOrderTicket(createdOrder?.order?.id as any);
      // const response = await fetch("http://localhost:3333/api/print-ticket", {
      //   method: "POST",
      //   headers: {
      //     "Content-Type": "application/json",
      //   },
      //   body: JSON.stringify({
      //     ticket: ticketContent,
      //   }),
      // });

      // const result = await response.json();
      // if (!response.ok) throw new Error(result.error);
      handleReset();
      onClose();
    } else {
      toast.error("Erreur lors de la confirmation de la commande", { position: "bottom-center" });
    }

    // Clear the basket
    // setPaymentAmount("0.00");
    // setBasketItems([]);
    // setSelectedTable(null);
    // setSelectedCustomer(null);
    // // Show a success toast
    // toast.success("Commande confirmée!", { position: "bottom-center" });

    // // Close the modal
    // onClose();
  };

  const handlePendingPayment = () => {
    // Handle logic for pending payment
    toast.success("Paiement en attente!", { position: "bottom-center" });
    setPaymentAmount("0.00");
    setBasketItems([]);
    setSelectedTable(null);
    setSelectedCustomer(null);

    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent onInteractOutside={(event) => event.preventDefault()} size="4xl">
        <div className="grid grid-cols-2 gap-4 mt-4 ">
          <div className="flex flex-col p-2">
            <div className="z-[9999]">
              <SelectCustomer setSelectedCustomer={setSelectedCustomer} selectedCustomer={selectedCustomer} customers={customers} />
              <div className="border-b pb-1 mt-2">
                <div className="flex justify-between text-sm text-gray-600">
                  <span>{selectedCustomer && isOpen ? ` ${selectedCustomer.firstName} ${selectedCustomer.lastName}` : " Non-Inscrit"}</span>
                  <span>
                    {selectedType === 2
                      ? "Sur place" //`Sur place - Table ${selectedTable.id}`
                      : "À emporter"}
                  </span>
                </div>
              </div>

              <div className="py-2 space-y-1">
                {basketItems &&
                  basketItems.map((item: any) => (
                    <div key={item.id} className="flex justify-between bg-gray-50 rounded-md p-2">
                      <div>
                        <span className="font-semibold">{item.qty}x</span> {item.product.name}
                      </div>
                      <span className="font-semibold">{item.amount} DH</span>
                    </div>
                  ))}
              </div>

              <div className="border-t pt-4 space-y-2">
                <div className="flex justify-between">
                  <span>Total HT:</span>
                  <span className="font-semibold">{(totalToPay - calculateTax).toFixed(2)} DH</span>
                </div>
                {taxes.length > 0 && taxes[0].taxRate > 0 ? (
                  <div className="flex mb-2 justify-between">
                    <span>TVA ({taxes[0].taxRate}%):</span>
                    <span>{calculateTax} DH</span>
                  </div>
                ) : null}
                {validCoupon && (
                  <div className="flex mb-2 justify-between">
                    <span>Remise Coupon ({validCoupon.code}):</span>
                    <span className="text-green-600">-{discountAmount.toFixed(2)} DH</span>
                  </div>
                )}
                <div className="flex justify-between font-bold">
                  <span>Total TTC:</span>
                  <span>{totalToPay} DH</span>
                </div>
              </div>
            </div>
          </div>

          <div className="p-2">
            <div className="flex items-center justify-between bg-white shadow-sm rounded-lg p-3 mb-4">
              <div>
                <p className="text-sm text-gray-500">Montant à payer</p>
                <p className="text-2xl font-bold text-green-600">{totalToPay} DH</p>
              </div>

              <div className="flex items-center space-x-4">
                <div>
                  <p className="text-sm text-gray-500">Crédit</p>
                  <p className="text-sm font-semibold text-gray-800">{parseFloat(paymentAmount).toFixed(2)} DH</p>
                </div>
                {(parseFloat(paymentAmount) - parseFloat(totalToPay)).toFixed(2) >= "0" ? (
                  <div>
                    <p className="text-sm text-gray-500">Monnaie</p>
                    <p className="text-sm font-semibold text-green-600">{(parseFloat(paymentAmount) - parseFloat(totalToPay)).toFixed(2)} DH</p>
                  </div>
                ) : (
                  <div>
                    <p className="text-sm text-gray-500">Restant</p>
                    <p className="text-sm font-semibold text-red-600">{(parseFloat(paymentAmount) - parseFloat(totalToPay)).toFixed(2)} DH</p>
                  </div>
                )}
              </div>
            </div>

            <Tabs defaultValue="cash" className="w-full inline-block">
              <TabsList className="w-full bg-transparent p-0 border-b-2  rounded-none">
                <TabsTrigger
                  className="capitalize w-full  data-[state=active]:shadow-none  data-[state=active]:bg-transparent data-[state=active]:text-primary transition duration-150 before:transition-all before:duration-150 relative before:absolute
         before:left-1/2 before:-bottom-[1px] before:h-[2px]
           before:-translate-x-1/2 before:w-0 data-[state=active]:before:bg-primary data-[state=active]:before:w-full"
                  value="cash"
                >
                  Espèces
                </TabsTrigger>
                <TabsTrigger
                  className="capitalize w-full data-[state=active]:shadow-none  data-[state=active]:bg-transparent data-[state=active]:text-primary transition duration-150 before:transition-all before:duration-150 relative before:absolute
         before:left-1/2 before:-bottom-[1px] before:h-[2px]
           before:-translate-x-1/2 before:w-0 data-[state=active]:before:bg-primary data-[state=active]:before:w-full"
                  value="other"
                >
                  Autres Modes
                </TabsTrigger>
              </TabsList>

              <TabsContent value="cash" className="mt-4">
                {/* Keypad UI */}
                <div className="px-1 mx-auto">
                  {/* Display */}
                  {/* <div className="flex items-center justify-end bg-white px-4 py-3 rounded-md text-xl font-bold border border-gray-300">
                    <span>{`${parseFloat(paymentAmount).toFixed(2)} €`}</span>
                  </div> */}
                  <input
                    type="text"
                    value={`${parseFloat(paymentAmount).toFixed(2)} DH`}
                    readOnly
                    className="w-full p-3 text-right text-2xl border rounded-lg"
                  />

                  {/* Keypad */}
                  <div className="grid grid-cols-3 gap-2 mt-4">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => (
                      <Button color="secondary" key={num} variant="outline" onClick={() => handleNumber(num.toString())} className="h-14 text-xl">
                        {num}
                      </Button>
                    ))}
                    <Button color="secondary" variant="outline" onClick={() => handleNumber("00")} className="h-14 text-xl">
                      00
                    </Button>

                    <Button color="secondary" variant="outline" onClick={() => handleNumber("0")} className="h-14 text-xl">
                      0
                    </Button>

                    <Button color="secondary" variant="outline" onClick={handleDelete} className="h-14">
                      <Icon icon="heroicons:backspace" className="h-6 w-6" />
                    </Button>

                    <Button color="secondary" variant="outline" onClick={handleDecimal} className="h-14 text-xl">
                      .
                    </Button>

                    <Button color="secondary" variant="outline" onClick={handleReset} className="h-14 col-span-2">
                      Annuler
                    </Button>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="other">
                {/* Other payment methods */}
                <div className="w-full p-4 space-y-4">
                  <p className="text-sm text-muted-foreground">Sélectionnez un mode de paiement</p>
                  <RadioGroup defaultValue="credit-card">
                    <div className="space-y-4 w-full">
                      <Card>
                        <CardContent className="flex items-center p-4">
                          <RadioGroupItem value="credit-card" id="credit-card" className="mr-4" />
                          <Label htmlFor="credit-card" className="flex-grow flex items-center">
                            <Icon icon="heroicons:credit-card" className="h-6 w-6 mr-3 text-orange-500" />

                            <div className="font-medium">Carte de Crédit</div>
                          </Label>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="flex items-center p-4">
                          <RadioGroupItem value="wallet" id="wallet" className="mr-4" />
                          <Label htmlFor="wallet" className="flex-grow flex items-center">
                            <Icon icon="heroicons:wallet" className="h-6 w-6 mr-3 text-orange-500" />

                            <div className="font-medium">Compte client</div>
                          </Label>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="flex items-center p-4">
                          <RadioGroupItem value="google-pay" id="google-pay" className="mr-4" />
                          <Label htmlFor="google-pay" className="flex-grow flex items-center">
                            <Icon icon="heroicons:device-phone-mobile" className="h-6 w-6 mr-4 text-blue-600" />

                            <div className="font-medium">Plateforme Mobile</div>
                          </Label>
                        </CardContent>
                      </Card>
                    </div>
                  </RadioGroup>
                </div>
              </TabsContent>
            </Tabs>

            <div className="flex gap-2 mt-auto">
              <Button onClick={handleConfirm} className="w-full mt-4" color="success">
                Confirmer le paiement
              </Button>
              <Button onClick={handlePendingPayment} className="w-full mt-4" color="warning">
                Paiement en attente
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const SelectTableModal = ({ isOpen, onClose, setSelectedTable, selectedTable, tables }: any) => {
  const [tablesData, setTablesData] = useState<any[]>([]);

  useEffect(() => {
    fetchTables();
  }, [isOpen]);

  const fetchTables = async () => {
    try {
      const tables = await getTables();
      setTablesData(tables || []);
    } catch (error) {
      console.error("Error fetching tables:", error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent onInteractOutside={(event) => event.preventDefault()} size="xl">
        <DialogHeader>
          <DialogTitle>Sélectionner une table</DialogTitle>
        </DialogHeader>

        {/* Option Buttons */}
        <div className="flex flex-wrap gap-4">
          {tablesData.map((table: any) => {
            const isSelected = selectedTable && selectedTable.id === table.id;
            const isDisabled = table.status === "OUT_OF_SERVICE" || table.status === "OCCUPIED";

            return (
              <div
                onClick={() => {
                  if (!isDisabled) {
                    setSelectedTable(table);
                    onClose();
                  }
                }}
                key={table.id}
                className={cn(
                  "cursor-pointer w-24 h-24 shadow-lg rounded-lg flex flex-col items-center justify-center",
                  {
                    "bg-primary-100 border-2 border-gray-900": isSelected,
                    "cursor-not-allowed opacity-50": isDisabled,
                  },
                  getStatusColor(table.status)
                )}
              >
                <Icon icon="material-symbols:table-restaurant-outline-rounded" className="h-8 w-8 mb-1" />
                <span className="font-semibold text-sm text-black">{table.id}</span>
                <span className="text-xs text-muted-foreground">
                  {table.status === "AVAILABLE" && "Disponible"}
                  {table.status === "OCCUPIED" && "Occupée"}
                  {table.status === "OUT_OF_SERVICE" && "Hors service"}
                </span>
              </div>
            );
          })}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const ProductModal = ({ isOpen, onClose, productData, addToBasket, setLoading }: any) => {
  const [quantity, setQuantity] = useState(1);
  const [selectedVariations, setSelectedVariations] = useState<any>([]);
  const [specialInstructions, setSpecialInstructions] = useState("");
  const { product, optionsAndVariations } = productData;

  useEffect(() => {
    const initialVariations = optionsAndVariations.map((item: any) => {
      return {
        option: item.option,
        variations: item.variations.length > 0 ? [item.variations[0]] : [],
      };
    });
    setSelectedVariations(initialVariations);
  }, [optionsAndVariations]);

  // Handle quantity change
  const handleQuantityChange = (type: any) => {
    setQuantity(type === "increment" ? quantity + 1 : Math.max(1, quantity - 1));
  };

  // Handle variation selection with maxAllowed logic
  const handleVariationSelect = (option: any, variation: any, maxAllowed: number = 1) => {
    setSelectedVariations((prevSelected: any) => {
      const optionIndex = prevSelected.findIndex((item: any) => item.option.id === option.id);

      // If the option is already selected
      if (optionIndex !== -1) {
        const currentVariations = prevSelected[optionIndex].variations;

        // If variation is already selected, deselect it
        if (currentVariations.some((v: any) => v.id === variation.id)) {
          const updatedVariations = currentVariations.filter((v: any) => v.id !== variation.id);

          // Update the selected option with new variations, or remove option if no variations left
          if (updatedVariations.length === 0) {
            return prevSelected.filter((item: any) => item.option.id !== option.id);
          }

          return [...prevSelected.slice(0, optionIndex), { option, variations: updatedVariations }, ...prevSelected.slice(optionIndex + 1)];
        }

        // If variation is not selected and within maxAllowed, add it
        if (currentVariations.length < maxAllowed) {
          return [
            ...prevSelected.slice(0, optionIndex),
            { option, variations: [...currentVariations, variation] },
            ...prevSelected.slice(optionIndex + 1),
          ];
        }

        // If maxAllowed reached, replace the last selected variation
        return [
          ...prevSelected.slice(0, optionIndex),
          { option, variations: [...currentVariations.slice(1), variation] },
          ...prevSelected.slice(optionIndex + 1),
        ];
      }

      // If option not selected, add it with the new variation
      return [...prevSelected, { option, variations: [variation] }];
    });
  };

  // Calculate total price based on selected variations
  const calculateTotalPrice = () => {
    const basePrice = product.price * quantity;

    const variationsPrice = selectedVariations
      .flatMap((item: any) => item.variations) // Flatten all selected variations
      .reduce((total: number, variation: any) => total + variation.price, 0); // Sum up the prices of selected variations

    return basePrice + variationsPrice * quantity;
  };

  const isAddToCartDisabled = optionsAndVariations.some((option: any) => {
    return !selectedVariations.some((selected: any) => selected.option.id === option.option.id && selected.variations.length > 0);
  });

  const handleAddToCart = () => {
    addToBasket(product, quantity, selectedVariations, calculateTotalPrice().toFixed(2), specialInstructions);
    onClose();
    toast.success("Produit ajouté au panier", { position: "bottom-center" });
    setSpecialInstructions("");
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        onInteractOutside={(event) => event.preventDefault()}
        size="xl"
        className="gap-1 space-y-0 max-h-[750px] overflow-x-auto [&::-webkit-scrollbar]:w-[5px]
                      [&::-webkit-scrollbar-track]:rounded-full
                      [&::-webkit-scrollbar-track]:bg-gray-100
                      [&::-webkit-scrollbar-thumb]:rounded-full
                      [&::-webkit-scrollbar-thumb]:bg-primary-300
                      dark:[&::-webkit-scrollbar-track]:bg-neutral-700
                      dark:[&::-webkit-scrollbar-thumb]:bg-neutral-50"
      >
        <div className="flex">
          <img src={product.imageUrl} alt={product.name} className=" w-[100px] h-[100px] object-cover rounded" />
          <div className="space-y-1 ml-7 flex flex-col justify-center">
            <div className="text-lg font-semibold">{product.name}</div>
            <div className="text-sm text-gray-500 ">{product.description}</div>
            <div className="text-lg font-semibold ">{product.price} DH</div>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <label className="block font-medium">Quantité : </label>
          <div className="text-sm font-medium text-default-600 py-2 flex items-center gap-2">
            <Button size="icon" variant="outline" className="h-8 w-8" color="primary" onClick={() => handleQuantityChange("decrement")}>
              <Icon icon="heroicons:minus" className="h-4 w-4" />
            </Button>
            <span>{quantity}</span>
            <Button size="icon" variant="outline" className="h-8 w-8" color="primary" onClick={() => handleQuantityChange("increment")}>
              <Icon icon="heroicons:plus" className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Options and variations */}
        {optionsAndVariations.map((item: any) => (
          <div key={item.option.id} className="flex flex-col space-y-2 pb-2">
            <label className="block font-medium">{item.option.name}</label>
            <div className="flex space-x-4">
              {item.variations.map((variation: any) => (
                <div
                  key={variation.id}
                  className={`flex items-center space-x-2 p-2 pr-4 border rounded-lg cursor-pointer ${
                    selectedVariations.some(
                      (selected: any) => selected.option.id === item.option.id && selected.variations.some((v: any) => v.id === variation.id)
                    )
                      ? "border-primary-500 text-primary-500 bg-primary-100"
                      : ""
                  }`}
                  onClick={() => handleVariationSelect(item.option, variation, item.maxAllowed)}
                >
                  <img src={variation.imageUrl} alt={variation.name} className="w-[40px] h-[40px] object-cover rounded" />
                  <div>
                    <div>{variation.name}</div>
                    <div className="font-semibold">+ {variation.price} DH</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* Special Instructions */}
        <div className="my-4">
          <label className="block font-medium mb-2">Instructions spéciales</label>
          <Textarea
            placeholder="Ajouter une note (mayo supplémentaire, fromage, etc.)"
            value={specialInstructions}
            onChange={(e) => setSpecialInstructions(e.target.value)}
          />
        </div>

        {/* Add to Cart Button */}
        <Button onClick={handleAddToCart} className="w-full mt-1" disabled={isAddToCartDisabled}>
          Ajouter au panier <span className="font-semibold ml-3"> {calculateTotalPrice().toFixed(2)} DH</span>
        </Button>
      </DialogContent>
    </Dialog>
  );
};
