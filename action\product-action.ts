"use server";

import { createProduct, deleteProduct, updateProduct } from "@/services/product";
import { IProduct, NewProductType } from "@/lib/interface";

export const AddProductAction = async (data: NewProductType) => {
  const response = await createProduct(data);
  return response;
};

export const UpdateProductAction = async (id: number, data: any) => {
  const response = await updateProduct(id, data);

  return response;
};

export const DeleteProductAction = async (id: number) => {
  const response = await deleteProduct(id);
  return response;
};
