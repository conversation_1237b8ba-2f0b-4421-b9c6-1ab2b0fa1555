"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { getUserRole } from "@/lib/auth";
import { UserRoleType } from "@/lib/interface";
import auth3Dark from "@/public/images/auth/auth3-dark.png";
import auth3Light from "@/public/images/auth/auth3-light.png";
import { useAuthStore } from "@/store";
import { signOut, useSession } from "next-auth/react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import LoginSessionCodeForm from "@/components/auth/login-session-code";
import SiteLogoView from "@/components/site-logo";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { format, formatDistance } from "date-fns";
import { fr } from "date-fns/locale";
import { Plus, BarChart3 } from "lucide-react";
import Loading from "@/components/ui/loading";
import { Icon } from "@iconify/react";

export function formatDateFR(dateString: string): string {
  const date = new Date(dateString);
  return format(date, "dd-MM-yyyy HH:mm", { locale: fr });
}

export function getDurationBetweenDates(start: string, end: string): string {
  const startDate = new Date(start);
  const endDate = new Date(end);
  return formatDistance(startDate, endDate, { locale: fr });
}

const SessionPageView = ({ trans }: any) => {
  const { data: sessionData, status } = useSession();
  const router = useRouter();
  const { posSession, setUserSession, setUserRole } = useAuthStore();
  const [isConfirmationPopupOpen, setConfirmationPopupOpen] = useState(false);
  const [isPasswordPopupOpen, setPasswordPopupOpen] = useState(false);

  useEffect(() => {
    const initialize = async () => {
      if (status === "authenticated") {
        const _userRole = getUserRole(sessionData.user.token);

        if (_userRole === UserRoleType.Admin) {
          setUserRole(UserRoleType.Admin);
        } else if (_userRole === UserRoleType.Owner) {
          setUserRole(UserRoleType.Owner);
        } else if (_userRole === UserRoleType.Manager) {
          setUserRole(UserRoleType.Manager);
        } else if (_userRole === UserRoleType.Staff) {
          setUserRole(UserRoleType.Staff);
        }

        if (posSession && posSession.status == "ACTIVE") {
          router.push("/pos");
        }
      }
    };

    initialize();
  }, []);

  const handleOpenNewSession = async () => {
    router.push("/auth/login");
  };

  const handleResumePreviousSession = () => {
    if (posSession) {
      if (posSession.redirectedFrom == "pos") {
        setPasswordPopupOpen(true);
      } else {
        router.push("/pos");
      }
    }
  };

  const handleLogout = () => {
    setUserSession(null);
    signOut();
  };

  const handleViewStatistics = () => {
    router.push("/session-stats");
  };

  return (
    <div className="loginwrapper flex justify-center items-center relative overflow-hidden">
      {/* Images de fond */}
      <Image src={auth3Dark} alt="image de fond" className="absolute top-0 left-0 w-full h-full light:hidden" />
      <Image src={auth3Light} alt="image de fond" className="absolute top-0 left-0 w-full h-full dark:hidden" />

      {/* Composant des onglets */}
      <div className="flex space-y-5 flex-col items-center w-full bg-transparent  max-w-xl rounded-xl relative z-10 p-6">
        <div className="text-center space-y-2 mb-10">
          <SiteLogoView className="h-16 mx-auto" />
        </div>
        {sessionData === null || posSession === null ? (
          <div className="w-full">
            <Loading />
          </div>
        ) : (
          <>
            <div className="flex justify-center items-center gap-6 w-full ">
              <div>
                <Avatar className=" h-24 w-24 border-4 border-white shadow-md">
                  <AvatarImage
                    src={sessionData?.user.imageUrl || "https://www.shareicon.net/data/512x512/2016/05/24/770137_man_512x512.png"}
                    alt="Profile"
                  />
                  <AvatarFallback className="bg-orange-200 text-orange-700 text-2xl">HL</AvatarFallback>
                </Avatar>
              </div>

              <div>
                <h2 className="text-2xl font-bold text-gray-800 mb-1">
                  {sessionData?.user.firstName} {sessionData?.user.lastName}
                </h2>
                <div className="flex flex-col md:flex-row gap-2 md:gap-6 text-sm text-gray-500 justify-center">
                  <div>
                    <span className="font-medium">Ouverture:</span>
                    <br /> {formatDateFR(posSession.openAt)}
                  </div>
                  <div>
                    <span className="font-medium">Fermeture:</span>
                    <br /> {formatDateFR(posSession.closedAt)}
                  </div>
                  <div>
                    <span className="font-medium">Durée:</span> <br />
                    <Badge variant="outline" className="bg-orange-100 text-orange-700 hover:bg-orange-200">
                      {getDurationBetweenDates(posSession.openAt, posSession.closedAt)}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-2 w-full mt-8">
              {posSession === null && (
                <Button onClick={handleOpenNewSession} className="w-full" size="lg">
                  <Plus className="mr-2 h-4 w-4" />
                  Ouvrir une nouvelle session
                </Button>
              )}
              {posSession && (
                <div className="flex  flex-col gap-4 justify-center">
                  <Button
                    onClick={handleResumePreviousSession}
                    className="bg-orange-500 hover:bg-orange-600 text-white flex items-center gap-2 px-6 py-5 rounded-lg shadow-md transition-all hover:shadow-lg"
                  >
                    Reprendre la session précédente
                  </Button>

                  <Button
                    onClick={handleViewStatistics}
                    variant="outline"
                    className="border-blue-300 text-blue-700 hover:text-blue-700 hover:bg-blue-50 flex items-center gap-2 px-6 py-5 rounded-lg shadow-sm"
                  >
                    <BarChart3 className="h-5 w-5" />
                    Voir les statistiques détaillées
                  </Button>

                  <Button
                    variant="outline"
                    onClick={handleLogout}
                    className="border-gray-300 text-gray-700 hover:text-gray-700 hover:bg-gray-50 flex items-center gap-2 px-6 py-5 rounded-lg shadow-sm"
                  >
                    <Icon icon="heroicons:arrow-right-start-on-rectangle-20-solid" className="h-5 w-5" />
                    Déconnexion
                  </Button>
                </div>
              )}
            </div>
          </>
        )}
      </div>

      {/* Popup de confirmation */}
      <AlertDialog open={isConfirmationPopupOpen} onOpenChange={() => setConfirmationPopupOpen(!isConfirmationPopupOpen)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmer la Déconnexion</AlertDialogTitle>
            <AlertDialogDescription>
              Êtes-vous sûr de vouloir deconnecter ? Cette action vous déconnectera de votre session actuelle.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel asChild>
              <Button variant="outline">Annuler</Button>
            </AlertDialogCancel>
            <AlertDialogAction asChild>
              <Button onClick={handleLogout}>Confirmer la Déconnexion</Button>
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Popup du mot de passe */}
      <Dialog open={isPasswordPopupOpen} onOpenChange={setPasswordPopupOpen}>
        <DialogContent hiddenCloseIcon>
          <div className="text-center my-4">
            <p className="">Utilisez le pavé numérique pour entrer votre code à 4 chiffres.</p>
          </div>
          <div className="w-full px-10 mb-6">
            <LoginSessionCodeForm trans={trans} loadedFrom="session" inputType="password" />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SessionPageView;
