import { api } from "@/config/axios.config";
import { IUnit, NewUnitType } from "@/lib/interface";

// Function to get the list of all units
export const getUnits = async (): Promise<IUnit[] | null> => {
  try {
    const response = await api.get<IUnit[]>("/units");
    return response.data;
  } catch (error) {
    console.error("Get units error:", error);
    return null;
  }
};

// Function to get a unit by ID
export const getUnitById = async (unitId: number): Promise<IUnit | null> => {
  try {
    const response = await api.get<IUnit>(`/units/${unitId}`);
    return response.data;
  } catch (error) {
    console.error(`Get unit by ID error: ${error}`);
    return null;
  }
};

// Function to create a new unit
export const createUnit = async (newUnit: NewUnitType): Promise<IUnit | null> => {
  try {
    const response = await api.post<IUnit>("/units", newUnit);
    console.log(response);
    return response.data;
  } catch (error) {
    console.error("Create unit error:", error);
    return null;
  }
};

// Function to update an existing unit by ID
export const updateUnit = async (unitId: number, updatedUnit: Partial<IUnit>): Promise<IUnit | null> => {
  try {
    const response = await api.patch<IUnit>(`/units/${unitId}`, updatedUnit);
    return response.data;
  } catch (error) {
    console.error("Update unit error:", error);
    return null;
  }
};

// Function to delete a unit by ID
export const deleteUnit = async (unitId: number): Promise<boolean> => {
  try {
    await api.delete(`/units/${unitId}`);
    return true;
  } catch (error) {
    console.error("Delete unit error:", error);
    return false;
  }
};
