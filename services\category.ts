import { api } from "@/config/axios.config";
import { ICategory, NewCategoryType } from "@/lib/interface";

// Function to get the list of categories
export const getCategories = async (): Promise<ICategory[] | null> => {
  try {
    const response = await api.get<ICategory[]>(`/categories/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}?availability=true`);
    return response.data;
  } catch (error) {
    console.error("Get Categories error:", error);
    return null;
  }
};

// Function to get the list of categories
interface ICategoryResponse {
  totalCount: number;
  data: ICategory[];
}

export const getCategoriesWithPagination = async (
  page: number = 0,
  size: number = 10,
  sortBy: string = "name",
  sortDirection: "asc" | "desc" = "asc"
): Promise<ICategoryResponse | null> => {
  try {
    const response = await api.get<ICategory[]>(`/categories?size=${size}&page=${page}&sort=${sortBy},${sortDirection}`);

    // Extract total count from headers and the data from the response
    const totalCount = parseInt(response.headers["x-total-count"]);
    const data: ICategory[] = response.data;

    return { totalCount, data };
  } catch (error) {
    console.error("Get Categories error:", error);
    return null;
  }
};

// Function to get the category by Id
export const getCategoryById = async (categoryId: number): Promise<ICategory | null> => {
  try {
    const response = await api.get<ICategory>(`/categories/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/${categoryId}`);
    return response.data;
  } catch (error) {
    console.error("Get Categories error:", error);
    return null;
  }
};

// Function to create a new category
export const createCategory = async (newCategory: NewCategoryType): Promise<ICategory | null> => {
  try {
    const response = await api.post<ICategory>(`/categories/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}`, newCategory);
    return response.data;
  } catch (error) {
    console.error("Create Category error:", error);
    return null;
  }
};

// Function to delete a category by ID
export const deleteCategory = async (categoryId: number): Promise<boolean> => {
  try {
    await api.delete(`/categories/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/${categoryId}`);
    return true;
  } catch (error) {
    console.error("Delete Category error:", error);
    return false;
  }
};

// Function to update an existing category by ID
export const updateCategory = async (categoryId: number, updatedCategory: any): Promise<ICategory | null> => {
  try {
    const response = await api.patch<ICategory>(`/categories/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/${categoryId}`, updatedCategory);
    return response.data;
  } catch (error) {
    console.error("Update Category error:", error);
    return null;
  }
};
