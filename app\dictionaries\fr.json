{"dashboard": "tableau de bord", "analytics": "Analyse", "ecommerce": "e-commerce", "project": "projet", "chat": "chat", "email": "Email", "kanban": "kanban", "task": "tâche", "calender": "calendrier", "projects": "projets", "base ui": "interface de base", "accordion": "<PERSON><PERSON><PERSON>", "alert": "alerte", "avatar": "avatar", "badge": "insigne", "breadcrumb": "fil d'<PERSON>ne", "button": "bouton", "card": "carte", "carousel": "carrousel", "color": "couleur", "combobox": "combobox", "command": "commande", "dropdown": "menu déroulant", "dialog": "dialogue", "kbd": "kbd", "pagination": "pagination", "popover": "pop-over", "progress": "progression", "sheet": "feuille", "skeleton": "squelette", "tabs": "onglets", "toast": "notification", "tooltip": "info-bulle", "typography": "typographie", "affix": "affixe", "calendar": "calendrier", "steps": "étapes", "timeline": "chronologie", "tour": "visite", "tree": "arbre", "watermark": "filigrane", "autocomplete": "autocomplétion", "checkbox": "case à cocher", "file uploader": "télé<PERSON><PERSON> de <PERSON>", "input": "entrée", "input group": "groupe d'entrée", "input mask": "masque d'entrée", "radio": "radio", "range slider": "<PERSON><PERSON> de plage", "rating": "évaluation", "select": "s<PERSON><PERSON><PERSON><PERSON>", "react select": "s<PERSON><PERSON><PERSON>", "switch": "interrupteur", "textarea": "zone de texte", "form layout": "mise en page du formulaire", "form elemenets": "éléments du formulaire", "form validation": "validation du formulaire", "use controller": "utiliser le contrôleur", "use form": "utiliser le formulaire", "form wizard": "assistant de formulaire", "pages": "pages", "error": "erreur", "utility": "utilitaire", "invoice": "facture", "sign in 01": "connexion 01", "sign in 02": "connexion 02", "sign in 03": "connexion 03", "sign in 04": "connexion 04", "sign in 05": "connexion 05", "sign up 01": "inscription 01", "sign up 02": "inscription 02", "sign up 03": "inscription 03", "sign up 04": "inscription 04", "sign up 05": "inscription 05", "forget password 01": "mot de passe oublié 01", "forget password 02": "mot de passe oublié 02", "forget password 03": "mot de passe oublié 03", "forget password 04": "mot de passe oublié 04", "forget password 05": "mot de passe oublié 05", "lock screen 01": "écran de verrouillage 01", "lock screen 02": "écran de verrouillage 02", "lock screen 03": "écran de verrouillage 03", "lock screen 04": "écran de verrouillage 04", "lock screen 05": "écran de verrouillage 05", "two step 01": "deux étapes 01", "two step 02": "deux étapes 02", "two step 03": "deux étapes 03", "two step 04": "deux étapes 04", "two step 05": "deux étapes 05", "password create 01": "création de mot de passe 01", "password create 02": "création de mot de passe 02", "password create 03": "création de mot de passe 03", "password create 04": "création de mot de passe 04", "password create 05": "création de mot de passe 05", "error 401": "erreur 401", "error 403": "erreur 403", "error 404": "erreur 404", "error 419": "erreur 419", "error 429": "erreur 429", "error 500": "erreur 500", "error 503": "erreur 503", "blank page": "page blanche", "comming soon": "bientôt disponible", "under maintenance": "sous maintenance", "user profile": "profil utilisateur", "overview": "<PERSON><PERSON><PERSON><PERSON>", "documents": "documents", "activities": "activités", "settings": "paramètres", "invoices": "factures", "create invoice": "créer une facture", "invoice list": "liste des factures", "email template": "mod<PERSON>le d'email", "welcome": "bienvenue", "authentication": "authentification", "advanced": "<PERSON><PERSON><PERSON>", "basic": "de base", "reset password 01": "réinitialiser le mot de passe 01", "reset password 02": "réinitialiser le mot de passe 02", "verify email": "vérifier l'email", "verify otp": "vérifier le OTP", "shop": "magasin", "shopping cart": "panier", "corporate": "corpora<PERSON><PERSON>", "agency": "agence", "blog": "blog", "photography": "photographie", "tables": "Tables", "simple table": "tableau simple", "tailwindui table": "tableau tailwindui", "data table": "tableau de données", "diagram": "diagrammes", "react flow": "flux react", "organization tree": "arbre organisationnel", "update node": "mettre à jour le nœud", "add node": "ajouter un nœud", "horizontal flow": "flux horizontal", "dagree tree": "arbre dagree", "download diagram": "télécharger le diagramme", "with minimap": "avec mini-carte", "with background": "avec arrière-plan", "panel position": "position du panneau", "chart": "graphique", "apex chart": "graphique apex", "rechart chart": "graphique rechart", "chart js": "chart js", "unovis": "unovis", "line": "ligne", "area": "zone", "column": "colonne", "bar": "barre", "combo/mixed": "combo/mélangé", "Range Area": "Zone de Plage", "funnel": "entonnoir", "candle stick": "bougie", "boxplot": "boîte à moustaches", "pie": "<PERSON><PERSON><PERSON>", "radar": "radar", "polar area": "zone polaire", "radial bars": "barres radiales", "bubble": "bulle", "scatter": "nuage de points", "heatmap": "carte de chaleur", "treemap": "carte des arbres", "composed": "composé", "tree map": "carte des arbres", "other": "autre", "scales": "<PERSON><PERSON><PERSON>", "scale options": "options d'échelle", "legend": "légende", "title": "titre", "subtitle": "sous-titre", "scriptable options": "options scriptables", "animations": "animations", "maps": "cartes", "google": "google", "vector": "vecteur", "react leaflet": "leaflet react", "unovis map": "carte unovis", "leaflet map": "carte leaflet", "leaflet flow": "flux leaflet", "leaflet advance": "leaflet avancé", "icons": "icônes", "hero icons": "ic<PERSON>s hero", "lucide icons": "icônes lucide", "custom icons": "icônes personnalisées", "multi level": "multi-niveau", "level 1.1": "niveau 1.1", "level 2": "niveau 2", "application": "application", "components": "composants", "forms": "formulaires", "menu": "menu", "addCategory": "A<PERSON>ter catégorie", "category": "<PERSON><PERSON><PERSON><PERSON>", "description": "Description", "availability": "Disponibilité", "availableFrom": "Disponible à partir de", "availableTo": "Disponible jusqu'à", "action": "Action", "inStock": "En Stock", "outOfStock": "<PERSON><PERSON>", "confirmDeletion": "Confirmer la <PERSON>", "warning": "Avertissement", "deleteConfirmation": "Êtes-vous sûr de vouloir supprimer cet élément ? Cette action est irréversible.", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "categoryDeleted": "Catégorie supprimée !", "deleteError": "E<PERSON>ur lors de la suppression de la catégorie", "products": "Produits", "product": "Produit", "list": "Lister", "addons": "Addons", "add product": "Ajouter produit", "categories": "Catégories", "add category": "A<PERSON>ter catégorie", "selectCategory": "--<PERSON><PERSON><PERSON><PERSON>ner une catégorie--", "variations": "Variations", "variation": "Variation", "add variation": "Ajouter variation", "options": "Options", "financial": "Financier", "coupons": "Coupons", "discounts": "Réductions", "taxes": "Taxes", "store": "Store", "categoryName": "Nom de la catégorie", "startTimeAvailability": "Heure de début de disponibilité", "endTimeAvailability": "Heure de fin de disponibilité", "uploadDescription": "Déposez l'image ici ou cliquez pour télécharger.", "categoryImage": "Image de la catégorie (taille maximale de 1 Mo à 3,5 Mo)", "reset": "Réinitialiser", "finish": "<PERSON><PERSON><PERSON>", "categoryCreated": "Catégorie c<PERSON> !", "errorCreatingCategory": "Erreur lors de la création de la catégorie", "addVariation": "Ajouter variation", "price": "Prix", "productDeleted": "Produit supprimé !", "deletionError": "<PERSON><PERSON>ur lors de la suppression du produit", "addProduct": "Ajouter produit", "isAddon": "Est un complément", "productAddedSuccess": "Produit ajouté avec succès !", "productCreationError": "Erreur lors de la création d'un nouveau produit", "productNameLabel": "Nom du produit", "productImageHeader": "Image du produit (taille maximale de 1 Mo à 3,5 Mo)", "availabilityHeader": "Disponibilité", "availabilityFromLabel": "Début de la disponibilité", "availabilityToLabel": "Fin de la disponibilité", "financialInfoHeader": "Informations financier", "discountLabel": "Remise", "taxLabel": "Taxe", "done": "<PERSON><PERSON><PERSON><PERSON>", "next": "Suivant", "assignmentOptionsVariations": "Affectation d'options et d'variations au produit", "assignmentSuccess": "Les options et les variations ont été affectés au produit avec succès", "option": "Option", "addAssignment": "Ajouter Affectation", "maxAllowed": "Max Autorisé", "selectOption": "Sélectionner option", "isDefault": "Est par défaut", "editProduct": "Modifier le produit", "productUpdatedSuccess": "Produit mis à jour avec succès !", "productUpdateError": "Erreur lors de la mise à jour du produit", "variationName": "Nom de variation", "variationCreateError": "Erreur lors de la création du nouvel variation", "variationAddedSuccess": "Variation ajouté avec succès !", "variationImage": "Image de variation (taille maximale de 1 Mo à 3,5 Mo)", "editVariation": "Modifier variation", "variationUpdateError": "Erreur lors de la mise à jour de variation", "variationUpdatedSuccess": "Variation mis à jour avec succès !", "productInformations": "Informations du produit", "optionsAndVariations": "Options et variations", "optionDeleteError": "Erreur lors de la suppression de l'option", "optionDeletedSuccess": "Option supprimée !", "noProducts": "Aucun produit trouvé", "noVariations": "Aucun variation trouvé", "noCategories": "<PERSON><PERSON>ne catégorie trouvée", "noOptions": "Aucune option trouvée", "categoryDeleteError": "E<PERSON>ur lors de la suppression de la catégorie", "categoryDeletedSuccess": "Catégorie supprimée !", "variationDeleteError": "Erreur lors de la suppression de variation", "variationDeletedSuccess": "Variation supprimé !", "productDeleteError": "<PERSON><PERSON>ur lors de la suppression du produit", "productDeletedSuccess": "Produit supprimé !", "optionName": "Nom de l'option", "editOptionTitle": "Modifier l'option", "optionUpdateError": "Erreur lors de la mise à jour de l'option", "optionUpdatedSuccess": "Option mise à jour !", "addOptionTitle": "Ajouter option", "optionCreationError": "Erreur lors de la création d'un nouveau modifier", "optionCreatedSuccess": "Modifier créé !", "editCategoryTitle": "Modifier la catégorie", "categoryImageLabel": "Image de la catégorie (taille maximale de 1 Mo à 3,5 Mo)", "categoryNameLabel": "Nom de la catégorie", "categoryUpdateError": "Erreur lors de la mise à jour de la catégorie", "categoryUpdatedSuccess": "Catégorie mise à jour !", "addAddon": "Ajouter un addon", "addonDeleteError": "<PERSON><PERSON>ur lors de la suppression de l'addon", "addonDeletedSuccess": "Addon supprimé !", "addonTableHead": "<PERSON><PERSON>", "addon": "<PERSON><PERSON>", "addonName": "Nom du addon", "addonImage": "Image du addon (taille maximale de 1 Mo à 3,5 Mo)", "addonCreatedSuccess": "<PERSON>don créé avec succès !", "addonCreationError": "Erreur lors de la création de l'addon", "editAddon": "Modifier l'addon", "storeName": "Nom du magasin", "address": "<PERSON><PERSON><PERSON>", "phone": "Téléphone", "website": "Site Web", "storeLogo": "Logo du magasin (taille maximale de 1 Mo à 3,5 Mo)", "storeSetup": "Configuration du magasin", "storeUpdatedSuccess": "Le magasin a été mis à jour avec succès", "storeUpdateError": "Erreur lors de la mise à jour du magasin", "loginSuccess": "Connexion réussie", "loginError": "Nom d'utilisateur ou mot de passe incorrect", "infoPrompt": "Entrez les informations que vous avez utilisées lors de votre inscription.", "username": "Nom d'utilisateur", "password": "Mot de passe", "rememberMe": "Se souvenir de moi", "forgetPassword": "Mot de passe oublié ?", "loading": "Chargement...", "signIn": "Se connecter", "apply": "Appliquer", "orders": "Commandes", "orderNumber": "<PERSON><PERSON><PERSON><PERSON>", "status": "Statut", "type": "Type", "createdAt": "<PERSON><PERSON><PERSON>", "kioskName": "Nom du kiosque", "DINE_IN": "Sur place", "TAKE_AWAY": "À emporter", "DELIVERY": "<PERSON><PERSON><PERSON>", "PENDING": "En attente", "PREPARING": "En préparation", "READY": "<PERSON><PERSON><PERSON><PERSON>", "COMPLETED": "<PERSON><PERSON><PERSON><PERSON>", "CANCELLED": "<PERSON><PERSON><PERSON>", "branch": "Branche", "branchUpdatedSuccess": "Branche mise à jour avec succès !", "branchUpdateError": "Erreur lors de la mise à jour de la branche.", "branchName": "Nom de la branche", "branchConfiguration": "Configuration de la branche", "branchLogo": "Logo de la branche (taille maximale de 1 Mo à 3,5 Mo)", "users": "Utilisateurs", "user": "Utilisa<PERSON>ur", "statut": "Statut", "roles": "<PERSON><PERSON><PERSON>", "activate": "Activer", "deactivate": "Désactiver", "changeRole": "<PERSON><PERSON> le <PERSON>", "updateInfo": "Mettre à jour les informations", "activated": "Activé", "nonActivated": "Non activé", "ROLE_USER": "Utilisa<PERSON>ur", "ROLE_MANAGER": "Gestionnaire", "ROLE_OWNER": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ROLE_ADMIN": "Administrateur", "units": "Unités", "unit": "Unité", "abbreviation": "Abréviation", "editUnitTitle": "Modifier l'unité", "addUnitTitle": "Ajouter une unité", "unitName": "Nom de l'unité", "unitCreatedSuccess": "Unité créée avec succès", "unitCreationError": "Erreur lors de la création de l'unité", "unitUpdatedSuccess": "Unité mise à jour avec succès", "unitUpdateError": "Erreur lors de la mise à jour de l'unité", "unitDeleteError": "Erreur lors de la suppression de l'unité", "unitDeletedSuccess": "Unité supprimée avec succès", "selectUnit": "--Sélectionner l'unité--", "inventory": "Inventaire", "inventoryTracked": "<PERSON><PERSON><PERSON>", "notTracked": "Non suivi", "isInventoryTracked": "Suivre l'inventaire ?", "kiosks": "Ki<PERSON><PERSON>", "noKiosks": "Aucun kiosque disponible.", "changeBranch": "Changer de succursale", "kiosk": "Kiosque", "IN_SERVICE": "En service", "OUT_OF_SERVICE": "Hors service", "DISABLED": "Désactivé", "add": "Ajouter", "edit": "Modifier", "language": "<PERSON><PERSON>", "translatedName": "Nom traduit", "translations": "Traductions"}