import { api } from "@/config/axios.config";
import { IOption, NewOptionType } from "@/lib/interface";

// Function to get the list of options
export const getOptions = async (): Promise<IOption[] | null> => {
  try {
    const response = await api.get<IOption[]>(`/options?size=500`);
    return response.data;
  } catch (error) {
    console.error("Get options error:", error);
    return null;
  }
};

// Function to get the list of options
interface IOptionResponse {
  totalCount: number;
  data: IOption[];
}

export const getOptionsWithPagination = async (
  page: number = 0,
  size: number = 10,
  sortBy: string = "name",
  sortDirection: "asc" | "desc" = "asc"
): Promise<IOptionResponse | null> => {
  try {
    const response = await api.get<IOption[]>(`/options?size=${size}&page=${page}&sort=${sortBy},${sortDirection}`);

    // Extract total count from headers and the data from the response
    const totalCount = parseInt(response.headers["x-total-count"]);
    const data: IOption[] = response.data;

    return { totalCount, data };
  } catch (error) {
    console.error("Get options error:", error);
    return null;
  }
};

// Function to create a new Option
export const createOption = async (newOption: NewOptionType): Promise<IOption | null> => {
  try {
    const response = await api.post<IOption>(`/options`, newOption);
    return response.data;
  } catch (error) {
    console.error("Create Option error:", error);
    return null;
  }
};

// Function to delete a Option by ID
export const deleteOption = async (OptionId: number): Promise<boolean> => {
  try {
    await api.delete(`/options/${OptionId}`);
    return true;
  } catch (error) {
    console.error("Delete Option error:", error);
    return false;
  }
};

// Function to update an existing Option by ID
export const updateOption = async (OptionId: number, updatedOption: Partial<IOption>): Promise<IOption | null> => {
  try {
    const response = await api.patch<IOption>(`/options/${OptionId}`, updatedOption);
    return response.data;
  } catch (error) {
    console.error("Update Option error:", error);
    return null;
  }
};

// Function to delete a Option by ID
export const getVariationsByOptionId = async (OptionId: number): Promise<IOption | null> => {
  try {
    let response = await api.get(`/options/${OptionId}`);
    return response.data.variations;
  } catch (error) {
    console.error("Delete Option error:", error);
    return null;
  }
};
