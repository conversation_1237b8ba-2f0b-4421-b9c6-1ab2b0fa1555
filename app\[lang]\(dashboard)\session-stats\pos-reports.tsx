import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Clock, User, Utensils, Wallet } from "lucide-react";

// Mock data (replace with real data in production)
const cashierData = {
  id: "C001",
  name: "<PERSON>",
  avatar: "/placeholder.svg?height=40&width=40",
  totalSales: 1250.75,
  transactionCount: 45,
  averageTransactionValue: 27.79,
};

const sessionData = {
  startTime: "2023-04-15T09:00:00",
  endTime: "2023-04-15T17:00:00",
  duration: "8 hours",
};

const cashDrawerData = {
  startingFloat: 200,
  totalCollected: 1250.75,
  currentBalance: 1450.75,
};

const topSellingItems = [
  { name: "Product A", quantity: 25, revenue: 250.0 },
  { name: "Product B", quantity: 20, revenue: 180.0 },
  { name: "Product C", quantity: 15, revenue: 225.0 },
  { name: "Product D", quantity: 10, revenue: 150.0 },
  { name: "Product E", quantity: 5, revenue: 100.0 },
];

const discountData = {
  total: 75.5,
  breakdown: [
    { type: "Senior Citizen", count: 5, value: 25.0 },
    { type: "Loyalty Program", count: 10, value: 50.5 },
  ],
};

// New mock data for restaurant tables summary
const tablesSummaryData = {
  totalTables: 20,
  occupiedTables: 15,
  averageTurnoverTime: "1 hour 30 minutes",
  topPerformingTable: {
    number: 7,
    revenue: 450.25,
    turnoverCount: 6,
  },
  tableDetails: [
    { number: 1, status: "Occupied", revenue: 120.5, turnoverCount: 3 },
    { number: 2, status: "Available", revenue: 85.75, turnoverCount: 2 },
    { number: 3, status: "Occupied", revenue: 200.0, turnoverCount: 4 },
    { number: 4, status: "Reserved", revenue: 0, turnoverCount: 0 },
    { number: 5, status: "Occupied", revenue: 150.25, turnoverCount: 3 },
  ],
};

export default function POSReports() {
  return (
    <div className="mx-auto  space-y-6">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* Cashier Performance Section */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="flex items-center gap-4">
              <Avatar className=" w-9 h-9">
                <AvatarImage
                  className=""
                  src={cashierData.avatar}
                  alt={cashierData.name}
                />
                <AvatarFallback>
                  {cashierData.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <h3 className="text-md font-semibold">
                <p>{cashierData.name}</p>
                <p className="text-xs text-muted-foreground">
                  ID: {cashierData.id}
                </p>
              </h3>
            </div>
          </CardHeader>
          <CardContent>
            {/* <div className="flex items-center space-x-4">
              <Avatar>
                <AvatarImage src={cashierData.avatar} alt={cashierData.name} />
                <AvatarFallback>
                  {cashierData.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium">{cashierData.name}</p>
                <p className="text-xs text-muted-foreground">
                  ID: {cashierData.id}
                </p>
              </div>
            </div> */}
            <div className="mt-4 space-y-2">
              <div className="flex justify-between">
                <p className="text-sm">Ventes Totales:</p>
                <p className="text-sm font-medium">
                  €{cashierData.totalSales.toFixed(2)}
                </p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm">Transactions:</p>
                <p className="text-sm font-medium">
                  {cashierData.transactionCount}
                </p>
              </div>
              {/* <div className="flex justify-between">
                <p className="text-sm">Moyenne Transaction:</p>
                <p className="text-sm font-medium">
                  ${cashierData.averageTransactionValue.toFixed(2)}
                </p>
              </div> */}
            </div>
          </CardContent>
        </Card>

        {/* Session Information */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="flex items-center gap-4">
              <div className="bg-green-100 p-2 rounded-full">
                <Clock className="w-5 h-5 text-green-600" />
              </div>
              <h3 className="text-md font-semibold">Informations de Session</h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <p className="text-sm">Heure de Début:</p>
                <p className="text-sm font-medium">
                  {new Date(sessionData.startTime).toLocaleString('fr-FR', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                  })}
                </p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm">Heure de Fin:</p>
                <p className="text-sm font-medium">
                  {new Date(sessionData.endTime).toLocaleString('fr-FR', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                  })}
                </p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm">Durée:</p>
                <p className="text-sm font-medium">{sessionData.duration}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Cash Drawer Details */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="flex items-center gap-4">
              <div className="bg-yellow-100 p-2 rounded-full">
                <Wallet className="w-5 h-5 text-yellow-600" />
              </div>
              <h3 className="text-md font-semibold">
                Détails du Tiroir Caissier
              </h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <p className="text-sm">Fonds de Départ:</p>
                <p className="text-sm font-medium">
                  €{cashDrawerData.startingFloat.toFixed(2)}
                </p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm">Total Collecté:</p>
                <p className="text-sm font-medium">
                  €{cashDrawerData.totalCollected.toFixed(2)}
                </p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm">Solde Actuel:</p>
                <p className="text-sm font-medium">
                  €{cashDrawerData.currentBalance.toFixed(2)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Restaurant Tables Summary */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="flex items-center gap-4">
              <div className="bg-primary-100 p-2 rounded-full">
                <Utensils className="w-5 h-5 text-primary-600" />
              </div>
              <h3 className="text-md font-semibold">
                Résumé des Tables du Restaurant
              </h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <p className="text-sm">Tables Totales:</p>
                <p className="text-sm font-medium">
                  {tablesSummaryData.totalTables}
                </p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm">Tables Occupées:</p>
                <p className="text-sm font-medium">
                  {tablesSummaryData.occupiedTables}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-2 items-center w-full h-full gap-6">
        {/* Top-Selling Items */}
        <Card className="w-full h-full">
          <CardHeader>
            <CardTitle>Meilleures Ventes</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nom de l'Article</TableHead>
                  <TableHead className="text-left">Quantité Vendue</TableHead>
                  <TableHead className="text-left">Revenu</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {topSellingItems.map((item) => (
                  <TableRow key={item.name}>
                    <TableCell>{item.name}</TableCell>
                    <TableCell className="text-left">{item.quantity}</TableCell>
                    <TableCell className="text-left">
                      €{item.revenue.toFixed(2)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Discount Summary */}
        <Card className="w-full h-full">
          <CardHeader>
            <CardTitle>Remises</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between font-medium">
                <p>Total des Remises Appliquées:</p>
                <p>€{discountData.total.toFixed(2)}</p>
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type de Remise</TableHead>
                    <TableHead className="text-left">Nombre</TableHead>
                    <TableHead className="text-left">Valeur</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {discountData.breakdown.map((discount) => (
                    <TableRow key={discount.type}>
                      <TableCell>{discount.type}</TableCell>
                      <TableCell className="text-left">
                        {discount.count}
                      </TableCell>
                      <TableCell className="text-left">
                        €{discount.value.toFixed(2)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
