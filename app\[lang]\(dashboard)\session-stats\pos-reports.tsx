import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Clock, User, Utensils, Wallet } from "lucide-react";
import { useSession } from "next-auth/react";

interface POSReportsProps {
  sessionData: any;
  kpiData: any;
  topProducts: any[];
}

export default function POSReports({ sessionData, kpiData, topProducts }: POSReportsProps) {
  const { data: userSession } = useSession();

  // Fallback data when real data is not available
  const cashierData = {
    id: userSession?.user?.id || "C001",
    name: userSession?.user ? `${userSession.user.firstName} ${userSession.user.lastName}` : "John Doe",
    avatar: userSession?.user?.imageUrl || "/placeholder.svg?height=40&width=40",
    totalSales: kpiData?.totalAmount || 0,
    transactionCount: kpiData?.totalOrders || 0,
    averageTransactionValue: kpiData?.averageOrderValue || 0,
  };

  const sessionInfo = {
    startTime: sessionData?.openAt || "2023-04-15T09:00:00",
    endTime: sessionData?.closedAt || "2023-04-15T17:00:00",
    duration: "8 hours", // This could be calculated from start/end times
  };

  const cashDrawerData = {
    startingFloat: 200, // This would come from session opening data
    totalCollected: kpiData?.totalAmount || 0,
    currentBalance: (kpiData?.totalAmount || 0) + 200,
  };

  const topSellingItems = topProducts?.slice(0, 5) || [{ name: "Aucun produit", quantity: 0, revenue: 0 }];

  const discountData = {
    total: kpiData?.totalDiscounts || 0,
    breakdown: [{ type: "Remises diverses", count: 1, value: kpiData?.totalDiscounts || 0 }],
  };

  // Mock data for restaurant tables summary (would need real API)
  const tablesSummaryData = {
    totalTables: 20,
    occupiedTables: 15,
    averageTurnoverTime: "1 hour 30 minutes",
    topPerformingTable: {
      number: 7,
      revenue: 450.25,
      turnoverCount: 6,
    },
    tableDetails: [
      { number: 1, status: "Occupied", revenue: 120.5, turnoverCount: 3 },
      { number: 2, status: "Available", revenue: 85.75, turnoverCount: 2 },
      { number: 3, status: "Occupied", revenue: 200.0, turnoverCount: 4 },
      { number: 4, status: "Reserved", revenue: 0, turnoverCount: 0 },
      { number: 5, status: "Occupied", revenue: 150.25, turnoverCount: 3 },
    ],
  };
  return <div className="mx-auto  space-y-6"></div>;
}
