"use client";

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Clock, Utensils, CreditCard } from "lucide-react";
import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { getTables } from "@/services/table";
import { format, formatDistance } from "date-fns";
import { fr } from "date-fns/locale";
import { Badge } from "@/components/ui/badge";

interface POSReportsProps {
  posSession: any;
  kpiData: any;
  topProducts: any[];
  paymentMethodSummary?: any[];
}

export function formatDateFR(dateString: any): any {
  const date = new Date(dateString);
  return format(date, "dd-MM-yyyy HH:mm", { locale: fr });
}

export function getDurationBetweenDates(start: string, end: string): string {
  const startDate = new Date(start);
  const endDate = new Date(end);
  return formatDistance(startDate, endDate, { locale: fr });
}

export default function POSReports({ kpiData, topProducts, posSession, paymentMethodSummary = [] }: POSReportsProps) {
  const { data: sessionData } = useSession();
  const [tables, setTables] = useState<any[]>([]);
  const [tablesLoading, setTablesLoading] = useState(false);

  // Fetch tables data
  useEffect(() => {
    const fetchTables = async () => {
      setTablesLoading(true);
      try {
        const tablesData = await getTables();
        if (tablesData) {
          setTables(tablesData);
        }
      } catch (error) {
        console.error("Failed to fetch tables:", error);
      } finally {
        setTablesLoading(false);
      }
    };

    fetchTables();
  }, []);

  // Helper function to get status text in French
  const getStatusTextFR = (status: string) => {
    switch (status) {
      case "AVAILABLE":
        return "Disponible";
      case "OCCUPIED":
        return "Occupée";
      case "CLEANING":
        return "Nettoyage";
      default:
        return status;
    }
  };

  // Calculate table statistics from real data
  const tablesSummaryData = {
    totalTables: tables.length,
    availableTables: tables.filter((t) => t.status === "AVAILABLE").length,
    occupiedTables: tables.filter((t) => t.status === "OCCUPIED").length,
    outOfServiceTables: tables.filter((t) => t.status === "CLEANING").length,
    tableDetails: tables
      .filter((t) => t.status === "OCCUPIED" || t.status === "CLEANING")
      .map((table) => ({
        id: table.id,
        name: table.name || `Table ${table.id}`,
        status: getStatusTextFR(table.status),
        statusCode: table.status,
      })),
  };

  return (
    <div className="mx-auto  space-y-6">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Cashier Performance Section */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="flex items-center gap-4">
              <div className="bg-green-100 p-2 rounded-full">
                <Clock className="w-5 h-5 text-green-600" />
              </div>
              <h3 className="text-md font-semibold">Informations de Session</h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <p className="text-sm">Heure de Début:</p>
                <p className="text-sm font-medium">{formatDateFR(posSession.openAt as any)}</p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm">Heure de Fin:</p>
                <p className="text-sm font-medium">{posSession.closedAt ? formatDateFR(posSession.closedAt as any) : "En cours"}</p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm">Durée:</p>
                <p className="text-sm font-medium">
                  <Badge variant="outline" className="bg-orange-100 text-orange-700 hover:bg-orange-200">
                    {getDurationBetweenDates(posSession.openAt, posSession.closedAt || new Date().toISOString())}
                  </Badge>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Methods Summary */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="flex items-center gap-4">
              <div className="bg-blue-100 p-2 rounded-full">
                <CreditCard className="w-5 h-5 text-blue-600" />
              </div>
              <h3 className="text-md font-semibold">Méthodes de Paiement</h3>
            </div>
          </CardHeader>
          <CardContent>
            {paymentMethodSummary.length > 0 ? (
              <div className="space-y-3">
                {paymentMethodSummary.map((method) => (
                  <div key={method.id} className="p-3 bg-slate-50 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-medium text-slate-700">{method.name}</h4>
                      <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-300">
                        {method.totalOrders} commandes
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-slate-600">Montant Total:</p>
                        <p className="font-semibold text-slate-800">{method.totalAmount.toFixed(2)} DH</p>
                      </div>
                      <div>
                        <p className="text-slate-600">Frais de Transaction:</p>
                        <p className="font-semibold text-slate-800">{method.transactionFees.toFixed(2)} DH</p>
                      </div>
                    </div>
                    {paymentMethodSummary.length > 1 && (
                      <div className="mt-2 pt-2 border-t border-slate-200">
                        <p className="text-xs text-slate-500">
                          {((method.totalAmount / paymentMethodSummary.reduce((sum, m) => sum + m.totalAmount, 0)) * 100).toFixed(1)}% du total
                        </p>
                      </div>
                    )}
                  </div>
                ))}
                <div className="mt-4 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                  <div className="flex justify-between items-center">
                    <span className="font-medium text-blue-800">Total Général:</span>
                    <span className="font-bold text-blue-900">
                      {paymentMethodSummary.reduce((sum, method) => sum + method.totalAmount, 0).toFixed(2)} DH
                    </span>
                  </div>
                  <div className="flex justify-between items-center mt-1">
                    <span className="text-sm text-blue-700">Commandes Totales:</span>
                    <span className="text-sm font-medium text-blue-800">
                      {paymentMethodSummary.reduce((sum, method) => sum + method.totalOrders, 0)}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-sm text-muted-foreground">Aucune méthode de paiement utilisée</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Restaurant Tables Summary */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="flex items-center gap-4">
              <div className="bg-primary-100 p-2 rounded-full">
                <Utensils className="w-5 h-5 text-primary-600" />
              </div>
              <h3 className="text-md font-semibold">Résumé des Tables du Restaurant</h3>
            </div>
          </CardHeader>
          <CardContent>
            {tablesLoading ? (
              <div className="text-center py-4">
                <p className="text-sm text-muted-foreground">Chargement des tables...</p>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-slate-50 rounded-lg">
                    <p className="text-2xl font-bold text-slate-700">{tablesSummaryData.totalTables}</p>
                    <p className="text-xs text-slate-600">Tables Totales</p>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <p className="text-2xl font-bold text-green-700">{tablesSummaryData.availableTables}</p>
                    <p className="text-xs text-green-600">Disponibles</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <p className="text-2xl font-bold text-blue-700">{tablesSummaryData.occupiedTables}</p>
                    <p className="text-xs text-blue-600">Occupées</p>
                  </div>
                  <div className="text-center p-3 bg-red-50 rounded-lg">
                    <p className="text-2xl font-bold text-red-700">{tablesSummaryData.outOfServiceTables}</p>
                    <p className="text-xs text-red-600">Hors Service</p>
                  </div>
                </div>
                {tablesSummaryData.tableDetails.length > 0 && (
                  <div className="mt-4">
                    <p className="text-sm font-medium mb-2">État des Tables:</p>
                    <div className="space-y-1">
                      {tablesSummaryData.tableDetails.map((table) => (
                        <div key={table.id} className="flex justify-between items-center py-1">
                          <span className="text-sm">{table.name}</span>
                          <Badge
                            variant="outline"
                            className={`text-xs ${
                              table.statusCode === "AVAILABLE"
                                ? "bg-green-100 text-green-700 border-green-300"
                                : table.statusCode === "OCCUPIED"
                                ? "bg-blue-100 text-blue-700 border-blue-300"
                                : "bg-red-100 text-red-700 border-red-300"
                            }`}
                          >
                            {table.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
