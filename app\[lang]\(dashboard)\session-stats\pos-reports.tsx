"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Clock, User, Utensils, Wallet } from "lucide-react";
import { useSession } from "next-auth/react";
import { format, formatDistance } from "date-fns";
import { fr } from "date-fns/locale";
import { Badge } from "@/components/ui/badge";

interface POSReportsProps {
  posSession: any;
  kpiData: any;
  topProducts: any[];
}

export function formatDateFR(dateString: string): string {
  const date = new Date(dateString);
  return format(date, "dd-MM-yyyy HH:mm", { locale: fr });
}

export function getDurationBetweenDates(start: string, end: string): string {
  const startDate = new Date(start);
  const endDate = new Date(end);
  return formatDistance(startDate, endDate, { locale: fr });
}

export default function POSReports({ kpiData, topProducts, posSession }: POSReportsProps) {
  const { data: sessionData } = useSession();

  // Mock data for restaurant tables summary (would need real API)
  const tablesSummaryData = {
    totalTables: 20,
    occupiedTables: 15,
    averageTurnoverTime: "1 hour 30 minutes",
    topPerformingTable: {
      number: 7,
      revenue: 450.25,
      turnoverCount: 6,
    },
    tableDetails: [
      { number: 1, status: "Occupied", revenue: 120.5, turnoverCount: 3 },
      { number: 2, status: "Available", revenue: 85.75, turnoverCount: 2 },
      { number: 3, status: "Occupied", revenue: 200.0, turnoverCount: 4 },
      { number: 4, status: "Reserved", revenue: 0, turnoverCount: 0 },
      { number: 5, status: "Occupied", revenue: 150.25, turnoverCount: 3 },
    ],
  };

  return (
    <div className="mx-auto  space-y-6">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Cashier Performance Section */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="flex items-center gap-4">
              {sessionData && (
                <Avatar className="h-12 w-12">
                  <AvatarImage
                    src={sessionData?.user?.imageUrl || "https://www.shareicon.net/data/512x512/2016/05/24/770137_man_512x512.png"}
                    alt="Profile"
                  />
                  <AvatarFallback className="bg-orange-200 text-orange-700">
                    {sessionData?.user?.firstName?.[0]}
                    {sessionData?.user?.lastName?.[0]}
                  </AvatarFallback>
                </Avatar>
              )}
              <h3 className="text-md font-semibold">
                <p>
                  {sessionData?.user?.firstName}
                  {sessionData?.user?.lastName}
                </p>
              </h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="mt-4 space-y-2">
              <div className="flex justify-between">
                <p className="text-sm">Ventes Totales:</p>
                <p className="text-sm font-medium">0 DH</p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm">Transactions:</p>
                <p className="text-sm font-medium">0</p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm">Moyenne Transaction:</p>
                <p className="text-sm font-medium">0 DH</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Session Information */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="flex items-center gap-4">
              <div className="bg-green-100 p-2 rounded-full">
                <Clock className="w-5 h-5 text-green-600" />
              </div>
              <h3 className="text-md font-semibold">Informations de Session</h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <p className="text-sm">Heure de Début:</p>
                <p className="text-sm font-medium">{formatDateFR(posSession.openAt)}</p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm">Heure de Fin:</p>
                <p className="text-sm font-medium">{posSession.closedAt ? formatDateFR(posSession.closedAt) : "En cours"}</p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm">Durée:</p>
                <p className="text-sm font-medium">
                  <Badge variant="outline" className="bg-orange-100 text-orange-700 hover:bg-orange-200">
                    {getDurationBetweenDates(posSession.openAt, posSession.closedAt || new Date().toISOString())}
                  </Badge>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Restaurant Tables Summary */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="flex items-center gap-4">
              <div className="bg-primary-100 p-2 rounded-full">
                <Utensils className="w-5 h-5 text-primary-600" />
              </div>
              <h3 className="text-md font-semibold">Résumé des Tables du Restaurant</h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <p className="text-sm">Tables Totales:</p>
                <p className="text-sm font-medium">{tablesSummaryData.totalTables}</p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm">Tables Occupées:</p>
                <p className="text-sm font-medium">{tablesSummaryData.occupiedTables}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
