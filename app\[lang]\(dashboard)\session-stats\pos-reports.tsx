"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Clock, Utensils } from "lucide-react";
import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { getTables } from "@/services/table";
import { format, formatDistance } from "date-fns";
import { fr } from "date-fns/locale";
import { Badge } from "@/components/ui/badge";

interface POSReportsProps {
  posSession: any;
  kpiData: any;
  topProducts: any[];
}

export function formatDateFR(dateString: string): string {
  const date = new Date(dateString);
  return format(date, "dd-MM-yyyy HH:mm", { locale: fr });
}

export function getDurationBetweenDates(start: string, end: string): string {
  const startDate = new Date(start);
  const endDate = new Date(end);
  return formatDistance(startDate, endDate, { locale: fr });
}

export default function POSReports({ kpiData, topProducts, posSession }: POSReportsProps) {
  const { data: sessionData } = useSession();
  const [tables, setTables] = useState<any[]>([]);
  const [tablesLoading, setTablesLoading] = useState(false);

  // Fetch tables data
  useEffect(() => {
    const fetchTables = async () => {
      setTablesLoading(true);
      try {
        const tablesData = await getTables();
        if (tablesData) {
          setTables(tablesData);
        }
      } catch (error) {
        console.error("Failed to fetch tables:", error);
      } finally {
        setTablesLoading(false);
      }
    };

    fetchTables();
  }, []);

  // Helper function to get status text in French
  const getStatusTextFR = (status: string) => {
    switch (status) {
      case "AVAILABLE":
        return "Disponible";
      case "OCCUPIED":
        return "Occupée";
      case "OUT_OF_SERVICE":
        return "Hors service";
      default:
        return status;
    }
  };

  // Calculate table statistics from real data
  const tablesSummaryData = {
    totalTables: tables.length,
    availableTables: tables.filter((t) => t.status === "AVAILABLE").length,
    occupiedTables: tables.filter((t) => t.status === "OCCUPIED").length,
    outOfServiceTables: tables.filter((t) => t.status === "OUT_OF_SERVICE").length,
    tableDetails: tables
      .filter((t) => t.status === "OCCUPIED" || t.status === "OUT_OF_SERVICE")
      .map((table) => ({
        id: table.id,
        name: table.name || `Table ${table.id}`,
        status: getStatusTextFR(table.status),
        statusCode: table.status,
      })),
  };

  return (
    <div className="mx-auto  space-y-6">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Cashier Performance Section */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="flex items-center gap-4">
              {sessionData && (
                <Avatar className="h-12 w-12">
                  <AvatarImage
                    src={sessionData?.user?.imageUrl || "https://www.shareicon.net/data/512x512/2016/05/24/770137_man_512x512.png"}
                    alt="Profile"
                  />
                  <AvatarFallback className="bg-orange-200 text-orange-700">
                    {sessionData?.user?.firstName?.[0]}
                    {sessionData?.user?.lastName?.[0]}
                  </AvatarFallback>
                </Avatar>
              )}
              <h3 className="text-md font-semibold">
                <p>
                  {sessionData?.user?.firstName}
                  {sessionData?.user?.lastName}
                </p>
              </h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <p className="text-sm">Heure de Début:</p>
                <p className="text-sm font-medium">{formatDateFR(posSession.openAt)}</p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm">Heure de Fin:</p>
                <p className="text-sm font-medium">{posSession.closedAt ? formatDateFR(posSession.closedAt) : "En cours"}</p>
              </div>
              <div className="flex justify-between">
                <p className="text-sm">Durée:</p>
                <p className="text-sm font-medium">
                  <Badge variant="outline" className="bg-orange-100 text-orange-700 hover:bg-orange-200">
                    {getDurationBetweenDates(posSession.openAt, posSession.closedAt || new Date().toISOString())}
                  </Badge>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Restaurant Tables Summary */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="flex items-center gap-4">
              <div className="bg-primary-100 p-2 rounded-full">
                <Utensils className="w-5 h-5 text-primary-600" />
              </div>
              <h3 className="text-md font-semibold">Résumé des Tables du Restaurant</h3>
            </div>
          </CardHeader>
          <CardContent>
            {tablesLoading ? (
              <div className="text-center py-4">
                <p className="text-sm text-muted-foreground">Chargement des tables...</p>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-slate-50 rounded-lg">
                    <p className="text-2xl font-bold text-slate-700">{tablesSummaryData.totalTables}</p>
                    <p className="text-xs text-slate-600">Tables Totales</p>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <p className="text-2xl font-bold text-green-700">{tablesSummaryData.availableTables}</p>
                    <p className="text-xs text-green-600">Disponibles</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <p className="text-2xl font-bold text-blue-700">{tablesSummaryData.occupiedTables}</p>
                    <p className="text-xs text-blue-600">Occupées</p>
                  </div>
                  <div className="text-center p-3 bg-red-50 rounded-lg">
                    <p className="text-2xl font-bold text-red-700">{tablesSummaryData.outOfServiceTables}</p>
                    <p className="text-xs text-red-600">Hors Service</p>
                  </div>
                </div>
                {tablesSummaryData.tableDetails.length > 0 && (
                  <div className="mt-4">
                    <p className="text-sm font-medium mb-2">État des Tables:</p>
                    <div className="space-y-1">
                      {tablesSummaryData.tableDetails.map((table) => (
                        <div key={table.id} className="flex justify-between items-center py-1">
                          <span className="text-sm">{table.name}</span>
                          <Badge
                            variant="outline"
                            className={`text-xs ${
                              table.statusCode === "AVAILABLE"
                                ? "bg-green-100 text-green-700 border-green-300"
                                : table.statusCode === "OCCUPIED"
                                ? "bg-blue-100 text-blue-700 border-blue-300"
                                : "bg-red-100 text-red-700 border-red-300"
                            }`}
                          >
                            {table.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
