import React, { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

const TagInput = ({
  allTags,
  selectedTags,
  setSelectedTags,
  size = "lg",
  className = "",
  disabled = false,
  requiresPrice = false,
  openPriceModal,
  removeAction,
  openPriceModalToUpdate,
  updatePrice = false,
}: any) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const tagSearchRef = useRef(null);
  const dropdownRef = useRef(null);

  // Filter tags based on searchTerm and selectedTags
  const filteredTags = allTags.filter(
    (tag: any) =>
      tag.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
      !selectedTags.some((selectedTag: any) => selectedTag.id === tag.id)
  );

  // Add a tag to the selectedTags array
  const addTag = (tag: any) => {
    console.log("tag", tag);
    if (requiresPrice) {
      openPriceModal({ ...tag, price: 0 });
      setSelectedTags([...selectedTags, { ...tag, price: 0 }]);
    } else {
      setSelectedTags([...selectedTags, tag]);
    }
    setSearchTerm("");
    setDropdownVisible(false);

    // Check if the tag requires a price and trigger the price modal
  };

  // Remove a tag from the selectedTags array
  const removeTag = (tagId: any) => {
    setSelectedTags(selectedTags.filter((tag: any) => tag.id !== tagId));
    if (removeAction)
      removeAction(selectedTags.filter((tag: any) => tag.id === tagId)[0].id);
  };

  // Handle clicks outside of the input and dropdown to close the dropdown
  const handleOutsideClick = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !(dropdownRef.current as Node).contains(event.target as Node) &&
      tagSearchRef.current &&
      !(tagSearchRef.current as Node).contains(event.target as Node)
    ) {
      setDropdownVisible(false);
    }
  };

  const updateTagPrice = (tag: any) => {
    openPriceModalToUpdate(tag);
  };
  useEffect(() => {
    document.addEventListener("click", handleOutsideClick);
    return () => {
      document.removeEventListener("click", handleOutsideClick);
    };
  }, []);

  return (
    <div className="relative">
      <div
        className={cn(
          "flex items-center flex-wrap border rounded-md py-0 px-1",
          className
        )}
      >
        {selectedTags.map((tag: any) => (
          <div
            key={tag.id}
            className="cursor-pointer flex items-center gap-2 border bg-primary text-white text-sm font-medium py-1 px-1.5 rounded mr-1 my-1"
            onClick={() => updatePrice && updateTagPrice(tag)}
          >
            <span>{tag.name}</span>
            {requiresPrice && (
              <div className="rounded-sm px-1 ">{tag.price}€</div>
            )}
            {disabled === false && (
              <button
                className=" text-white hover:text-red-500"
                onClick={() => removeTag(tag.id)}
                type="button"
              >
                &times;
              </button>
            )}
          </div>
        ))}

        {disabled === false && (
          <Input
            ref={tagSearchRef}
            value={searchTerm}
            size={size}
            disabled={disabled}
            onChange={(e) => setSearchTerm(e.target.value)}
            onFocus={() => setDropdownVisible(true)}
            className="flex-grow p-4 focus:outline-none border-none"
            style={{ flex: 1, minWidth: "150px" }}
          />
        )}
      </div>

      {dropdownVisible && filteredTags.length > 0 && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full bg-white border border-gray-300 rounded-md mt-1"
        >
          <ul className="max-h-40 overflow-y-auto">
            {filteredTags.map((tag: any) => (
              <li
                key={tag.id}
                className="p-2 cursor-pointer hover:bg-blue-500 hover:text-white"
                onClick={() => addTag(tag)}
              >
                {tag.name}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default TagInput;
