import Credentials from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import GithubProvider from "next-auth/providers/github";
import { authenticateByCode } from "@/services/authenticate";
import { getAccount } from "@/services/account";
import { IUser } from "./interface";
import { NextAuthOptions } from "next-auth";
import { jwtDecode } from "jwt-decode";

declare module "next-auth" {
  interface Session {
    user: IUser & { token: string };
  }

  interface JWT {
    user: IUser & { token: string };
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.AUTH_GOOGLE_ID as string,
      clientSecret: process.env.AUTH_GOOGLE_SECRET as string,
    }),
    GithubProvider({
      clientId: process.env.AUTH_GITHUB_ID as string,
      clientSecret: process.env.AUTH_GITHUB_SECRET as string,
    }),
    Credentials({
      name: "credentials",
      credentials: {
        code: { label: "code", type: "text" },
      },
      async authorize(credentials: any): Promise<any> {
        const { code, posId } = credentials;

        const authResponse = await authenticateByCode({
          code,
        });

        if (!authResponse) {
          return null;
        }

        const user = await getAccount(authResponse.access_token);

        if (!user || !user.activated) {
          return null;
        }

        return { ...user, token: authResponse.access_token };
      },
    }),
  ],
  secret: process.env.AUTH_SECRET,

  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.user = user as any & { token: string };
      }
      return token;
    },
    async session({ session, token }) {
      if (token.user) {
        session.user = token.user as IUser & { token: string };
      }
      return session;
    },
  },
  pages: {
    signIn: "/auth/login",
    signOut: "/auth/logout",
  },

  debug: process.env.NODE_ENV !== "production",
};

interface DecodedToken {
  username: string;
  sub: number;
  roles: string[];
  iat: number;
  exp: number;
}

/**
 * Decodes a JWT token and returns its payload.
 * @param token - The JWT token to decode.
 * @returns The decoded token payload.
 */
export function decodeToken(token: string): DecodedToken {
  try {
    const decoded: DecodedToken = jwtDecode<DecodedToken>(token);
    return decoded;
  } catch (error) {
    console.error("Invalid token:", error);
    throw new Error("Failed to decode token");
  }
}

/**
 * Returns the user's role from the decoded JWT token.
 * @param token - The JWT token to decode.
 * @returns The user's role as a string.
 */
export function getUserRole(token: string): string {
  try {
    const decoded = decodeToken(token);

    // Assuming the roles array exists and has at least one role
    if (decoded.roles && decoded.roles.length > 0) {
      return decoded.roles[0]; // Return the first role, you can adjust based on your needs
    } else {
      throw new Error("No roles found in token");
    }
  } catch (error) {
    console.error("Error extracting role:", error);
    throw new Error("Failed to extract user role from token");
  }
}
