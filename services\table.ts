import { api } from "@/config/axios.config";

// Function to get the list of Tables
export const getTables = async (): Promise<any[] | null> => {
  try {
    const response = await api.get<any[]>(`/tables/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}`);
    return response.data;
  } catch (error) {
    console.error("Get Tables error:", error);
    return null;
  }
};

// Function to get the list of Tables
interface anyResponse {
  totalCount: number;
  data: any[];
}

export const getTablesWithPagination = async (
  page: number = 0,
  size: number = 10,
  sortBy: string = "name",
  sortDirection: "asc" | "desc" = "asc"
): Promise<anyResponse | null> => {
  try {
    const response = await api.get<any[]>(`/tables?size=${size}&page=${page}&sort=${sortBy},${sortDirection}`);

    // Extract total count from headers and the data from the response
    const totalCount = parseInt(response.headers["x-total-count"]);
    const data: any[] = response.data;

    return { totalCount, data };
  } catch (error) {
    console.error("Get Tables error:", error);
    return null;
  }
};

// Function to get the Table by Id
export const getTableById = async (tableId: number): Promise<any | null> => {
  try {
    const response = await api.get<any>(`/tables/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/${tableId}`);
    return response.data;
  } catch (error) {
    console.error("Get Tables error:", error);
    return null;
  }
};

// Function to delete a Table by ID
export const deleteTable = async (tableId: number): Promise<boolean> => {
  try {
    await api.delete(`/tables/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/${tableId}`);
    return true;
  } catch (error) {
    console.error("Delete Table error:", error);
    return false;
  }
};

// Function to update an existing Table by ID
export const updateTable = async (tableId: number, updatedTable: any): Promise<any | null> => {
  try {
    const response = await api.patch<any>(`/tables/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/${tableId}`, {
      ...updatedTable,
      branchId: process.env.NEXT_PUBLIC_BRANCH_ID,
    });
    return response.data;
  } catch (error) {
    console.error("Update Table error:", error);
    return null;
  }
};
