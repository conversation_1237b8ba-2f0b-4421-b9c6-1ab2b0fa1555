"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { formatDate, formatTime, getStatusColor, getTypeColor, translateOrderStatus, translateOrderType } from "@/lib/utils";
import { generateOrderTicket, getOrderWithDetails } from "@/services/order";
import { Icon } from "@iconify/react";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";

const columns = [
  {
    id: 1,
    label: "Produit",
  },
  {
    id: 2,
    label: "Quantité",
  },
  {
    id: 3,
    label: "Prix",
  },
  {
    id: 4,
    label: "Total",
  },
];

interface OrderItemCardProps {
  selectedOrder: any;
  onClose: () => void;
  trans: any;
}

const OrderItemCard = ({ selectedOrder, onClose, trans }: OrderItemCardProps) => {
  const [orderDetails, setOrderDetails] = useState<any>({});

  useEffect(() => {
    fetchData();
  }, [selectedOrder]);

  const fetchData = async () => {
    if (!selectedOrder) {
      return;
    }

    try {
      const data = await getOrderWithDetails(selectedOrder.id as any);

      if (data) {
        setOrderDetails(data);
      }
    } catch (err) {
      console.error("Error fetching order details:", err);
    }
  };

  const printTicket = async () => {
    try {
      const ticketContent = await generateOrderTicket(selectedOrder.id);
      console.log(ticketContent);
      const response = await fetch("http://localhost:3333/api/print-ticket", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ticket: ticketContent,
        }),
      });

      const result = await response.json();
      if (!response.ok) throw new Error(result.error);

      toast.success("Ticket printed successfully!", { position: "bottom-center" });
    } catch (error) {
      console.error("Printing failed:", error);
      alert("Failed to print ticket");
    }
  };

  if (!selectedOrder) return null;

  return (
    <div className="overflow-y-auto">
      <div className="grid grid-cols-12 gap-6">
        <div className="col-span-12">
          <div className="flex-none w-full md:w-auto">
            <div className="grid grid-cols-1">
              {/* Date and Time */}
              <div className="flex items-center p-2 border-b">
                <Icon icon="heroicons:calendar" className="h-4 w-4 mr-2 text-primary-500" />
                <span className="text-sm font-medium text-default-600">
                  {formatDate(orderDetails?.order?.createdAt)} à {formatTime(orderDetails?.order?.createdAt)}
                </span>
              </div>

              {/* Status Row */}
              <div className="flex items-center justify-between p-2 border-b">
                <span className="text-sm font-medium text-default-600 flex items-center">
                  <Icon icon="heroicons:check-badge" className="h-4 w-4 mr-2 text-primary-500" />
                  Statut
                </span>
                <Badge variant="soft" color={getStatusColor(orderDetails?.order?.status)} className="capitalize min-w-[100px] justify-center">
                  {translateOrderStatus(orderDetails?.order?.status, trans)}
                </Badge>
              </div>

              {/* Order Type Row */}
              <div className="flex items-center justify-between p-2 border-b">
                <span className="text-sm font-medium text-default-600 flex items-center">
                  <Icon icon="heroicons:shopping-bag" className="h-4 w-4 mr-2 text-primary-500" />
                  Type de commande
                </span>
                <Badge variant="soft" color={getTypeColor(orderDetails?.order?.type) as any} className="capitalize min-w-[100px] justify-center">
                  {translateOrderType(orderDetails?.order?.type, trans)}
                </Badge>
              </div>

              {orderDetails?.payment && (
                <>
                  {/* Payment Status Row */}
                  <div className="flex items-center justify-between p-2 border-b">
                    <span className="text-sm font-medium text-default-600 flex items-center">
                      <Icon icon="heroicons:credit-card" className="h-4 w-4 mr-2 text-primary-500" />
                      Statut de paiement
                    </span>
                    <Badge variant="soft" color={getStatusColor(orderDetails?.payment?.status)} className="capitalize min-w-[100px] justify-center">
                      {translateOrderStatus(orderDetails?.payment?.status, trans)}
                    </Badge>
                  </div>

                  {/* Payment Method Row */}
                  <div className="flex items-center justify-between p-2 border-b">
                    <span className="text-sm font-medium text-default-600 flex items-center">
                      <Icon icon="heroicons:banknotes" className="h-4 w-4 mr-2 text-primary-500" />
                      Mode de paiement
                    </span>
                    <span className="text-sm font-semibold text-default-600">{orderDetails?.payment?.paymentMethod?.name}</span>
                  </div>

                  {/* Payment Amount Row */}
                  <div className="flex items-center justify-between p-2 border-b">
                    <span className="text-sm font-medium text-default-600 flex items-center">
                      <Icon icon="heroicons:currency-dollar" className="h-4 w-4 mr-2 text-primary-500" />
                      Montant
                    </span>
                    <span className="text-sm font-bold text-primary-600 dark:text-primary-400">{orderDetails?.payment?.amount} DH</span>
                  </div>
                </>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 w-full gap-6 mt-4">
            <div className="flex flex-col">
              <Table>
                <TableHeader>
                  <TableRow>
                    {columns.map((column, index) => (
                      <TableHead key={`invoice-table-${index}`} className="text-default-600 ltr:last:text-right rtl:last:text-left">
                        {column.label}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody className="[&_tr:last-child]:border-1">
                  {orderDetails?.orderDetails?.map((item: any, index: number) => (
                    <TableRow key={`invoice-description-${index}`}>
                      <TableCell className="text-sm font-medium text-default-600 max-w-[200px] truncate">
                        <div className="flex gap-3 items-center">
                          {/* <img
                            defaultValue={"https://adstandards.com.au/wp-content/uploads/2023/08/food_and_beverage.svg"}
                            src={
                              item?.product?.imageUrl != null && item?.product?.imageUrl != ""
                                ? item?.product?.imageUrl
                                : "https://adstandards.com.au/wp-content/uploads/2023/08/food_and_beverage.svg"
                            }
                            width={40}
                          /> */}

                          <div className="space-y-2">
                            <div className="text-sm font-medium text-card-foreground">{item?.product?.name}</div>

                            <div className="space-y-1">
                              {item?.optionsAndVariations?.map((ov: any) => (
                                <div key={ov.option.id} className="text-xs text-muted-foreground">
                                  <span className="font-semibold">{ov.option.name}:</span>
                                  <span className="ml-1">
                                    {ov.variations.map((v: any, idx: number) => (
                                      <span key={v.id}>
                                        {v.name}
                                        {idx !== ov.variations.length - 1 && ", "}
                                      </span>
                                    ))}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="text-sm font-medium text-default-600">x{item?.quantity}</TableCell>
                      <TableCell className="text-sm font-medium text-default-600 whitespace-nowrap">{item?.product?.price} DH</TableCell>
                      <TableCell className="ltr:text-right rtl:text-left text-sm font-medium text-default-600">
                        {item?.product?.price * item?.quantity} DH
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>

          <div className="mt-8 flex gap-4 justify-end">
            <Button className="text-xs font-semibold" onClick={printTicket}>
              <Icon icon="heroicons:printer" className="w-5 h-5 ltr:mr-1 rtl:ml-1" /> Imprimer Ticket
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderItemCard;
