"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { formatDate, formatTime, getStatusColor, getTypeColor, translateOrderStatus, translateOrderType } from "@/lib/utils";
import { generateOrderTicket, getOrderWithDetails } from "@/services/order";
import { Icon } from "@iconify/react";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { Card, CardHeader, CardTitle, CardContent } from "../ui/card";

const columns = [
  {
    id: 1,
    label: "Produit",
  },
  {
    id: 2,
    label: "Quantité",
  },
  {
    id: 3,
    label: "Prix",
  },
  {
    id: 4,
    label: "Total",
  },
];

interface OrderItemCardProps {
  orderDetails: any;
  onClose: () => void;
  trans: any;
}

const OrderItemCard = ({ orderDetails, onClose, trans }: OrderItemCardProps) => {
  const printTicket = async () => {
    try {
      const ticketContent = await generateOrderTicket(orderDetails.id);
      console.log(ticketContent);
      const response = await fetch("http://localhost:3333/api/print-ticket", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ticket: ticketContent,
        }),
      });

      const result = await response.json();
      if (!response.ok) throw new Error(result.error);

      toast.success("Ticket printed successfully!", { position: "bottom-center" });
    } catch (error) {
      console.error("Printing failed:", error);
      alert("Failed to print ticket");
    }
  };

  if (!orderDetails) return null;

  return (
    <>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
        {/* Tableau des produits */}
        <div
          className="lg:col-span-2 max-h-[460px] overflow-x-auto [&::-webkit-scrollbar]:w-[5px]
                      [&::-webkit-scrollbar-track]:rounded-full
                      [&::-webkit-scrollbar-track]:bg-gray-100
                      [&::-webkit-scrollbar-thumb]:rounded-full
                      [&::-webkit-scrollbar-thumb]:bg-primary-300
                      dark:[&::-webkit-scrollbar-track]:bg-neutral-700
                      dark:[&::-webkit-scrollbar-thumb]:bg-neutral-5"
        >
          <div className="border rounded-lg overflow-hidden">
            <Table>
              <TableHeader className="bg-muted/50">
                <TableRow>
                  <TableHead className="w-[300px]">Produit</TableHead>
                  <TableHead className="text-center">Quantité</TableHead>
                  <TableHead className="text-right">Prix unitaire</TableHead>
                  <TableHead className="text-right">Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {orderDetails?.items?.map((item: any, index: number) => (
                  <TableRow key={`item-${index}`} className="hover:bg-muted/50">
                    <TableCell>
                      <div className="flex items-center gap-4">
                        <img
                          src={item?.product?.imageUrl || "https://adstandards.com.au/wp-content/uploads/2023/08/food_and_beverage.svg"}
                          alt={item?.product?.name}
                          className="w-10 h-10 rounded-md object-cover"
                        />
                        <div>
                          <div className="font-medium">{item?.product?.name}</div>
                          {item?.options?.length > 0 && (
                            <div className="text-sm text-muted-foreground mt-1 space-y-1">
                              {item?.options?.map((option: any) => (
                                <div key={option.id}>
                                  <span className="font-medium">{option.name}:</span>
                                  <div className="ml-2">
                                    {option.variations.map((variation: any) => (
                                      <div key={variation.id} className="flex items-center">
                                        <span>- {variation.name}</span>
                                        {variation.price > 0 && <span className="ml-2 text-primary-500">(+{variation.price} DH)</span>}
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">{item?.quantity}</TableCell>
                    <TableCell className="text-right">{item?.basePrice} DH</TableCell>
                    <TableCell className="text-right font-medium">{item?.totalPrice} DH</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Panneau latéral avec résumé */}
        <div className="space-y-6">
          {/* Informations de statut */}
          <div className="p-6 border rounded-lg bg-muted/50">
            <h3 className="font-medium mb-4">Statut de la commande</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Statut :</span>
                <Badge variant="soft" color={getStatusColor(orderDetails?.status as any)} className="capitalize">
                  {translateOrderStatus(orderDetails?.status, trans)}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Type :</span>
                <Badge variant="soft" color={getTypeColor(orderDetails?.type as any)} className="capitalize">
                  {translateOrderType(orderDetails?.type, trans)}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Paiement :</span>
                <Badge variant="soft" color={getStatusColor(orderDetails?.payment?.status as any)} className="capitalize">
                  {translateOrderStatus(orderDetails?.payment?.status, trans)}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Méthode :</span>
                <span className="text-sm font-medium">{orderDetails?.payment?.paymentMethod?.name || "Non spécifié"}</span>
              </div>
            </div>
          </div>

          {/* Résumé financier */}
          <div className="p-6 border rounded-lg bg-muted/50">
            <h3 className="font-medium mb-4">Résumé</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Produits :</span>
                <span className="text-sm font-medium">{orderDetails?.summary?.itemsCount || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Sous-total :</span>
                <span className="text-sm font-medium">{orderDetails?.summary?.subtotal || 0} DH</span>
              </div>

              {/* Taxes */}
              {orderDetails?.taxBreakdown?.totalTax > 0 && (
                <div className="space-y-2 pl-4 border-l-2 border-primary-200">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Taxes :</span>
                    <span className="text-sm font-medium">{orderDetails?.taxBreakdown?.totalTax || 0} DH</span>
                  </div>

                  {orderDetails?.taxBreakdown?.branchTax > 0 && (
                    <div className="flex justify-between">
                      <span className="text-xs text-muted-foreground">- Taxe établissement :</span>
                      <span className="text-xs">{orderDetails?.taxBreakdown?.branchTax || 0} DH</span>
                    </div>
                  )}

                  {orderDetails?.taxBreakdown?.categoryTax > 0 && (
                    <div className="flex justify-between">
                      <span className="text-xs text-muted-foreground">- Taxe catégorie :</span>
                      <span className="text-xs">{orderDetails?.taxBreakdown?.categoryTax || 0} DH</span>
                    </div>
                  )}

                  {orderDetails?.taxBreakdown?.productTax > 0 && (
                    <div className="flex justify-between">
                      <span className="text-xs text-muted-foreground">- Taxe produit :</span>
                      <span className="text-xs">{orderDetails?.taxBreakdown?.productTax || 0} DH</span>
                    </div>
                  )}
                </div>
              )}

              {/* Remises */}
              {(orderDetails?.summary?.totalDiscounts > 0 || orderDetails?.summary?.couponDiscount > 0) && (
                <div className="space-y-2 pl-4 border-l-2 border-primary-200">
                  <div className="flex justify-between text-destructive">
                    <span className="text-sm text-muted-foreground">Remises :</span>
                    <span className="text-sm font-medium">-{orderDetails?.summary?.totalDiscounts || 0} DH</span>
                  </div>

                  {orderDetails?.summary?.couponDiscount > 0 && (
                    <div className="flex justify-between">
                      <span className="text-xs text-muted-foreground">- Coupon :</span>
                      <span className="text-xs text-destructive">-{orderDetails?.summary?.couponDiscount || 0} DH</span>
                    </div>
                  )}

                  {orderDetails?.summary?.totalProductDiscounts > 0 && (
                    <div className="flex justify-between">
                      <span className="text-xs text-muted-foreground">- Remise produit :</span>
                      <span className="text-xs text-destructive">-{orderDetails?.summary?.totalProductDiscounts || 0} DH</span>
                    </div>
                  )}
                </div>
              )}

              {/* Total */}
              <div className="flex justify-between pt-3 border-t font-medium">
                <span>Total :</span>
                <span>{orderDetails?.summary?.totalAmount || orderDetails?.payment?.amount || 0} DH</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Boutons d'action */}
      <div className="mt-2 flex gap-4 justify-end">
        <Button onClick={printTicket}>
          <Icon icon="heroicons:printer" className="w-5 h-5 mr-2" />
          Imprimer le ticket
        </Button>
      </div>
    </>
  );
};

export default OrderItemCard;
