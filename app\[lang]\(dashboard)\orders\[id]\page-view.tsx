"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { useEffect, useState } from "react";

import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

import { Badge } from "@/components/ui/badge";
import { formatDate, formatTime, getStatusColor, getTypeColor, translateOrderStatus, translateOrderType } from "@/lib/utils";
import { generateOrderTicket, getOrderWithDetails } from "@/services/order";
import { Icon } from "@iconify/react";
import { useParams } from "next/navigation";
import toast from "react-hot-toast";

const columns = [
  {
    id: 1,
    label: "Produit",
  },
  {
    id: 2,
    label: "Quantité",
  },
  {
    id: 3,
    label: "Prix",
  },
  {
    id: 4,
    label: "Total",
  },
];

const OrderItemPageView = ({ trans }: any) => {
  const [orderDetails, setOrderDetails] = useState<any>({});
  const params = useParams();
  const id = params.id;

  useEffect(() => {
    fetchData();
  }, [id]);

  const fetchData = async () => {
    if (!id) {
      return;
    }

    try {
      const data = await getOrderWithDetails(id as any);

      if (data) {
        setOrderDetails(data);
      }
    } catch (err) {
      console.error("Error fetching order details:", err);
    }
  };

  const printTicket = async () => {
    try {
      const ticketContent = await generateOrderTicket(id as any);
      console.log(ticketContent);
      const response = await fetch("http://localhost:3333/api/print-ticket", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ticket: ticketContent,
        }),
      });

      const result = await response.json();
      if (!response.ok) throw new Error(result.error);

      toast.success("Ticket printed successfully!", { position: "bottom-center" });
    } catch (error) {
      console.error("Printing failed:", error);
      alert("Failed to print ticket");
    }
  };

  return (
    <div className="mb-4 p-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">Détails de la commande</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-12 gap-6">
            <div className="col-span-12">
              <div className="flex gap-6 flex-col md:flex-row">
                <div className="flex-1">
                  <div className="text-2xl font-semibold text-default-900">Commande #{orderDetails?.order?.orderNumber}</div>
                  <div className="mt-1.5 text-md font-medium text-default-600">
                    <div className="flex ga items-center">
                      <Icon icon="heroicons:calendar" className="h-4 w-4 mr-1" />
                      {formatDate(orderDetails?.order?.createdAt)}
                      <Icon icon="heroicons:clock" className="h-4 w-4 mr-1 ml-2" />
                      {formatTime(orderDetails?.order?.createdAt)}
                    </div>

                    <div className="flex items-center">
                      <Icon icon="heroicons:map-pin" className="h-4 w-4 mr-1" />
                      {orderDetails?.order?.branch?.name}
                    </div>
                    <div className="flex items-center">
                      <Icon icon="heroicons:device-tablet" className="h-4 w-4 mr-1" />
                      {orderDetails?.order?.kiosk ? orderDetails?.order?.kiosk?.name : orderDetails?.order?.pos?.name}
                    </div>
                  </div>
                </div>
                <div className="flex-none md:text-end">
                  <div className="mb-3 text-end flex justify-end">
                    <span className="text-sm font-medium text-default-600 ">Statut :</span>
                    <span className="text-sm font-medium text-default-600  text-end w-[100px] md:w-[100px] block">
                      <Badge variant="soft" color={getStatusColor(orderDetails?.order?.status as any)} className="capitalize">
                        {translateOrderStatus(orderDetails?.order?.status, trans)}
                      </Badge>
                    </span>
                  </div>
                  <div className="mb-3 text-end flex justify-end">
                    <span className="text-sm font-medium text-default-600 ">Type de commande :</span>
                    {/* <span className="text-sm font-medium text-default-600  text-end w-[100px] md:w-[100px] block">
                      <Badge variant="soft" color={getTypeColor(orderDetails?.order?.type as any)} className="capitalize">
                        {translateOrderType(orderDetails?.order?.type, trans)}
                      </Badge>
                    </span> */}
                  </div>
                  <div className="mb-3 text-end flex justify-end">
                    <span className="text-sm font-medium text-default-600 ">Statut de paiement :</span>
                    <span className="text-sm font-bold text-default-600  text-end w-[100px] md:w-[100px] block">
                      <Badge variant="soft" color={getStatusColor(orderDetails?.payment?.status as any)} className="capitalize">
                        {translateOrderStatus(orderDetails?.payment?.status, trans)}
                      </Badge>
                    </span>
                  </div>
                  <div className="mb-3 text-end flex justify-end">
                    <span className="text-sm font-medium text-default-600 ">Mode de paiement :</span>
                    <span className="text-sm font-bold text-default-600  text-end w-[100px] md:w-[100px] block">
                      {orderDetails?.payment?.paymentMethod.name}
                    </span>
                  </div>

                  <div className="mb-3 text-end flex justify-end">
                    <span className="text-sm font-medium text-default-600 ">Montant du paiement :</span>
                    <span className="text-sm font-bold text-default-600  text-end w-[100px] md:w-[100px] block">
                      {orderDetails?.payment?.amount} DH
                    </span>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-1 w-full gap-6 mt-6">
                <div className="flex flex-col">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        {columns.map((column, index) => (
                          <TableHead key={`invoice-table-${index}`} className="text-default-600 ltr:last:text-right rtl:last:text-left">
                            {column.label}
                          </TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody className="[&_tr:last-child]:border-1">
                      {orderDetails?.orderDetails?.map((item: any, index: number) => (
                        <TableRow key={`invoice-description-${index}`}>
                          <TableCell className="text-sm font-medium text-default-600 max-w-[200px] truncate">
                            <div className="flex gap-3 items-center">
                              <img
                                defaultValue={"https://adstandards.com.au/wp-content/uploads/2023/08/food_and_beverage.svg"}
                                src={
                                  item?.product?.imageUrl != null && item?.product?.imageUrl != ""
                                    ? item?.product?.imageUrl
                                    : "https://adstandards.com.au/wp-content/uploads/2023/08/food_and_beverage.svg"
                                }
                                width={40}
                              />

                              <div className="space-y-2">
                                <div className="text-sm font-medium text-card-foreground">{item?.product?.name}</div>

                                <div className="space-y-1">
                                  {item?.optionsAndVariations?.map((ov: any) => (
                                    <div key={ov.option.id} className="text-xs text-muted-foreground">
                                      <span className="font-semibold">{ov.option.name}:</span>
                                      <span className="ml-1">
                                        {ov.variations.map((v: any, idx: number) => (
                                          <span key={v.id}>
                                            {v.name}
                                            {idx !== ov.variations.length - 1 && ", "}
                                          </span>
                                        ))}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-sm font-medium text-default-600">x{item?.quantity}</TableCell>
                          <TableCell className="text-sm font-medium text-default-600 whitespace-nowrap">{item?.product?.price} DH</TableCell>
                          <TableCell className="ltr:text-right rtl:text-left text-sm font-medium text-default-600">
                            {item?.product?.price * item?.quantity} DH
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
                {/* <div className="w-full p-6 border border-default-300 rounded-md h-full">
                  <div>
                    {calculatedData.map((item, index) => (
                      <div
                        key={`invoice-item-${index}`}
                        className="mb-3 text-end flex justify-end"
                      >
                        <span className="text-sm font-medium text-default-600 ">
                          {item.label}:
                        </span>
                        <span className="text-sm font-medium text-default-600  text-end w-[100px] md:w-[160px] block">
                          ${item.amount}
                        </span>
                      </div>
                    ))}
                  </div>
                </div> */}
              </div>

              <div className="mt-8 flex gap-4 justify-end">
                <Button className="text-xs font-semibold " onClick={printTicket}>
                  <Icon icon="heroicons:printer" className="w-5 h-5 ltr:mr-1 rtl:ml-1" /> Imprimer Ticket
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OrderItemPageView;
