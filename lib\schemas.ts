import { z } from "zod";

export const AddAndUpdateCategorySchema = z.object({
  name: z.string().min(3, { message: "Category name must be at least 3 charecters." }),
  description: z.string().nullable(),
  availableFrom: z.string().nullable(),
  availableTo: z.string().nullable(),
});

export const AddAndUpdateProductSchema = z.object({
  name: z.string().min(3, { message: "Product name must be at least 3 characters." }),
  description: z.string().min(3, { message: "Product description must be at least 8 characters." }),
  price: z
    .string()
    .transform((value) => parseFloat(value))
    .refine((value) => !isNaN(value), {
      message: "Price must be a valid number.",
    }),
  //imageUrl: z.string().url().nullable(),
  availabilityStatus: z.boolean().nullable().default(true),
  availableFrom: z.string().nonempty({ message: "Available from date is required." }),
  availableTo: z.string().nonempty({ message: "Available to date is required." }),
  categoryId: z.string({ required_error: "Category is required." }),
  unitId: z.string({ required_error: "Unit is required." }),
});

export const UpdateProductSchema = z.object({
  name: z.string().min(3, { message: "Product name must be at least 3 characters." }),
  description: z.string().nullable(),
  price: z
    .string()
    .transform((value) => parseFloat(value))
    .refine((value) => !isNaN(value), {
      message: "Price must be a valid number.",
    }),
  //imageUrl: z.string().url().nullable(),
  //availabilityStatus: z.boolean().nullable().default(true),
  availableFrom: z.string().nullable(),
  availableTo: z.string().nullable(),
  categoryId: z.string({ required_error: "Category is required." }),
  unitId: z.string({ required_error: "Unit is required." }),
});

export const AddAndUpdateVariationSchema = z.object({
  name: z.string().min(3, { message: "Variation name must be at least 3 characters." }),
  description: z.string().nullable(),

  // imageUrl: z.string().url().nullable(),
  availabilityStatus: z.boolean().nullable().default(true),
  optionId: z.string({ required_error: "Option is required." }),
  unitId: z.string({ required_error: "Unit is required." }),
});

export const AddAndUpdateDiscountSchema = z.object({
  name: z.string().min(3, { message: "Discount name must be at least 3 characters." }),
  discountType: z.string().nullable(),
  discountValue: z.number({ required_error: "Discount value is required." }),
  startDate: z.date({ required_error: "Start date is required." }),
  endDate: z.date({ required_error: "End date is required." }),
  store: z
    .object({
      id: z.number(),
      name: z.string(),
      address: z.string().nullable(),
      phone: z.string().nullable(),
      email: z.string().nullable(),
      logoUrl: z.string().nullable(),
      website: z.string().nullable(),
    })
    .nullable(),
});

export const AddAndUpdateOptionSchema = z.object({
  name: z.string().min(3, { message: "Option name must be at least 3 charecters." }),

  // maxAllowed: z
  //   .string()
  //   .transform((value) => parseFloat(value))
  //   .refine((value) => !isNaN(value), {
  //     message: "Max Allowed must be a valid number.",
  //   }),
  // availabilityStatus: z.boolean().nullable().default(true),
});

export const AddAndUpdateUnitSchema = z.object({
  name: z.string().min(3, { message: "Unit name must be at least 3 charecters." }),
  abbreviation: z.string().min(1, { message: "Unit abbreviation must be at least 1 charecters." }),
});

// Store Schema
export const AddAndUpdateStoreSchema = z.object({
  name: z.string().min(3, { message: "Discount name must be at least 3 characters." }),
  address: z.string().nullable(),
  phone: z.string().nullable(),
  email: z.string().nullable(),
  website: z.string().nullable(),
});

// Branch Schema
export const AddAndUpdateBranchSchema = z.object({
  name: z.string().min(3, { message: "Discount name must be at least 3 characters." }),
  address: z.string().nullable(),
  phone: z.string().nullable(),
  email: z.string().nullable(),
});
