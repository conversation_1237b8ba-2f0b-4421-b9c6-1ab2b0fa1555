{"name": "quickorder-pos", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^40.1.0", "@ckeditor/ckeditor5-react": "^6.2.0", "@dnd-kit/core": "^6.0.8", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.2", "@emoji-mart/data": "^1.1.2", "@emoji-mart/react": "^1.1.1", "@formatjs/intl-localematcher": "^0.5.4", "@fullcalendar/core": "^6.1.13", "@fullcalendar/daygrid": "^6.1.9", "@fullcalendar/interaction": "^6.1.9", "@fullcalendar/list": "^6.1.9", "@fullcalendar/react": "^6.1.9", "@fullcalendar/timegrid": "^6.1.9", "@headlessui/react": "^2.2.0", "@hookform/resolvers": "^3.3.2", "@iconify/react": "^4.1.1", "@mdx-js/loader": "^3.0.1", "@mdx-js/react": "^3.0.1", "@next/mdx": "^14.1.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.4", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-direction": "^1.0.1", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-hover-card": "^1.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.3", "@radix-ui/react-navigation-menu": "^1.1.3", "@radix-ui/react-popover": "^1.0.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.4", "@radix-ui/react-select": "^1.2.2", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-tooltip": "^1.0.6", "@react-email/components": "^0.0.16", "@serialport/parser-readline": "^13.0.0", "@smastrom/react-rating": "^1.4.0", "@south-paw/react-vector-maps": "^3.2.0", "@tanstack/react-query": "^5.8.3", "@tanstack/react-query-devtools": "^5.8.3", "@tanstack/react-table": "^8.9.8", "@types/cleave.js": "^1.4.12", "@types/mdx": "^2.0.11", "@types/negotiator": "^0.6.3", "@types/react-flatpickr": "^3.8.11", "@types/react-syntax-highlighter": "^15.5.13", "@unovis/react": "^1.3.1", "@unovis/ts": "^1.3.1", "@vercel/analytics": "^1.2.2", "apexcharts": "^3.42.0", "autoprefixer": "10.4.15", "axios": "^1.6.1", "bcrypt": "^5.1.1", "chart.js": "^4.4.0", "class-variance-authority": "^0.7.0", "cleave.js": "^1.6.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "d3-shape": "^3.2.0", "dagre": "^0.8.5", "date-fns": "^3.3.1", "dayjs": "^1.11.10", "embla-carousel-auto-height": "^8.0.0-rc17", "embla-carousel-autoplay": "^8.0.0-rc17", "embla-carousel-react": "^8.0.0-rc17", "embla-carousel-wheel-gestures": "^8.0.0-rc05", "emoji-mart": "^5.5.2", "eslint": "8.48.0", "eslint-config-next": "14.0.1", "framer-motion": "^10.16.3", "google-map-react": "^2.2.1", "html-to-image": "^1.11.11", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lucide": "^0.270.0", "lucide-react": "^0.269.0", "moment": "^2.29.4", "negotiator": "^0.6.3", "next": "14.1.3", "next-auth": "^4.24.5", "next-themes": "^0.2.1", "nextra": "^2.13.4", "nextra-theme-docs": "^2.13.4", "postcss": "8.4.28", "quill": "^1.3.7", "rc-tree": "^5.8.2", "react": "18.2.0", "react-apexcharts": "^1.4.1", "react-chartjs-2": "^5.2.0", "react-day-picker": "^8.8.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "18.2.0", "react-draggable": "^4.4.6", "react-dropzone": "^14.2.3", "react-email": "^2.1.1", "react-flatpickr": "^3.10.13", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-leaflet": "^4.2.1", "react-quilljs": "^1.3.3", "react-resizable-panels": "^0.0.55", "react-select": "^5.8.0", "react-shepherd": "^4.2.0", "react-simple-keyboard": "^3.8.41", "react-syntax-highlighter": "^15.5.0", "react-wrap-balancer": "^1.1.0", "reactflow": "^11.10.4", "recharts": "^2.9.3", "sass": "^1.66.1", "serialport": "^13.0.0", "simplebar-react": "^3.2.4", "sonner": "^1.3.1", "styled-components": "^6.1.1", "swiper": "^11.0.5", "tailwind-merge": "^1.14.0", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.6", "unist-util-visit": "^5.0.0", "vaul": "^0.8.0", "zod": "^3.22.4", "zustand": "^4.4.1"}, "devDependencies": {"@faker-js/faker": "^8.2.0", "@svgr/webpack": "^8.1.0", "@types/next": "^9.0.0", "@types/node": "^20.10.0", "@types/react": "^18.2.38", "@types/react-dom": "^18.3.0", "typescript": "^5.4.5"}}