import { api } from "@/config/axios.config";

// ENUM for period types
export type PeriodType = "DAILY" | "WEEKLY" | "MONTHLY" | "YEARLY";

// Get general order summary by period
export const getOrderSummaryByPeriod = async (startDate: Date, endDate: Date): Promise<any | null> => {
  try {
    const response = await api.get(
      `/order-summary/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/by-date-range?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`
    );
    return response.data;
  } catch (error) {
    console.error("Order Summary Error:", error);
    return null;
  }
};

// Get top-selling products by period
export const getTopSellingProducts = async (startDate: Date, endDate: Date): Promise<any[] | null> => {
  try {
    const response = await api.get(
      `/order-summary/branch/${
        process.env.NEXT_PUBLIC_BRANCH_ID
      }/top-selling-products/by-date-range?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`
    );
    return response.data;
  } catch (error) {
    console.error("Top Selling Products Error:", error);
    return null;
  }
};

// Get status summary by period
export const getStatusSummary = async (startDate: Date, endDate: Date): Promise<any | null> => {
  try {
    const response = await api.get(
      `/order-summary/branch/${
        process.env.NEXT_PUBLIC_BRANCH_ID
      }/status-summary?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`
    );
    return response.data;
  } catch (error) {
    console.error("Status Summary Error:", error);
    return null;
  }
};

// Get category summary by period
export const getCategorySummary = async (startDate: Date, endDate: Date): Promise<any[] | null> => {
  try {
    const response = await api.get(
      `/order-summary/branch/${
        process.env.NEXT_PUBLIC_BRANCH_ID
      }/category-summary/by-date-range?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`
    );
    return response.data;
  } catch (error) {
    console.error("Category Summary Error:", error);
    return null;
  }
};

// Get category summary by period
export const getPaymentMethodSummary = async (startDate: Date, endDate: Date): Promise<any[] | null> => {
  try {
    const response = await api.get(
      `/order-summary/branch/${
        process.env.NEXT_PUBLIC_BRANCH_ID
      }/payment-method-summary/by-date-range?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`
    );
    return response.data;
  } catch (error) {
    console.error("Payment Method Summary Error:", error);
    return null;
  }
};

// Frontend service
export const getYearlyOrderSummary = async (): Promise<any[] | null> => {
  try {
    const response = await api.get(`/order-summary/branch/${process.env.NEXT_PUBLIC_BRANCH_ID}/date/${new Date()}`);
    return response.data;
  } catch (error) {
    console.error("Yearly Summary Error:", error);
    return null;
  }
};

// Get devices order summary
export const getDevicesOrderSummary = async (startDate: Date, endDate: Date): Promise<any[] | null> => {
  try {
    const response = await api.get(
      `/order-summary/branch/${
        process.env.NEXT_PUBLIC_BRANCH_ID
      }/device-summary/by-date-range?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`
    );
    return response.data;
  } catch (error) {
    console.error("Devices Summary Error:", error);
    return null;
  }
};
