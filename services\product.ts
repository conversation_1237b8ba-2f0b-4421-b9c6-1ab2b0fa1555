import { api } from "@/config/axios.config";
import { IProduct, NewProductType } from "@/lib/interface";

// Function to get products by category ID
export const getProductsByCategory = async (categoryId: number): Promise<IProduct[] | []> => {
  try {
    const response = await api.get<IProduct[]>(`/products/category/${categoryId}?availability=true`);
    return response.data;
  } catch (error) {
    console.error("Get products by category error:", error);
    return [];
  }
};

// Function to get the list of Products
export const getProducts = async (): Promise<IProduct[] | []> => {
  try {
    const response = await api.get<IProduct[]>(`/products/isAddon/false`);

    return response.data;
  } catch (error) {
    console.error("Get products error:", error);
    return [];
  }
};

// Function to get the list of Products
interface IProductResponse {
  totalCount: number;
  data: IProduct[];
}

export const getProductsWithPagination = async (
  page: number = 0,
  size: number = 10,
  sortBy: string = "name",
  sortDirection: "asc" | "desc" = "asc"
): Promise<IProductResponse | []> => {
  try {
    const response = await api.get<IProduct[]>(`/products/branch/1/is-addon/false?size=${size}&page=${page}&sort=${sortBy},${sortDirection}`);
    const totalCount = parseInt(response.headers["x-total-count"]);
    const data: IProduct[] = response.data;

    return { totalCount, data };
  } catch (error) {
    console.error("Get products error:", error);
    return [];
  }
};

// Function to create a new Product
export const createProduct = async (newProduct: NewProductType): Promise<IProduct | null> => {
  try {
    const response = await api.post<IProduct>(`/products`, newProduct);
    return response.data;
  } catch (error) {
    console.error("Create Product error:", error);
    return null;
  }
};

// Function to delete a Product by ID
export const deleteProduct = async (productId: number): Promise<boolean> => {
  try {
    await api.delete(`/products/${productId}`);
    return true;
  } catch (error) {
    console.error("Delete Product error:", error);
    return false;
  }
};

// Function to get the category by Id
export const getProductById = async (productId: number): Promise<IProduct | null> => {
  try {
    const response = await api.get<IProduct>(`/products/${productId}`);
    return response.data;
  } catch (error) {
    console.error("Get Products error:", error);
    return null;
  }
};

// Function to create a new Product
export const getProductAddons = async (productId: number): Promise<IProduct[] | null> => {
  try {
    const response = await api.get<IProduct[]>(`/addons/product/${productId}`);
    return response.data;
  } catch (error) {
    console.error("Get Products error:", error);
    return null;
  }
};

// Function to update an existing Product by ID
export const updateProduct = async (ProductId: number, updatedProduct: Partial<IProduct>): Promise<IProduct | null> => {
  try {
    const response = await api.patch<IProduct>(`/products/${ProductId}`, updatedProduct);
    return response.data;
  } catch (error) {
    console.error("Update Product error:", error);
    return null;
  }
};

export const getAddons = async (branchId: number): Promise<IProduct[] | []> => {
  try {
    const response = await api.get<IProduct[]>(`/products/isAddon/true`);

    return response.data;
  } catch (error) {
    console.error("Get products error:", error);
    return [];
  }
};

// Function to create a new Product
export const createProductAddons = async (data: { productId: number; addonIds: number[] }): Promise<any[] | null> => {
  const { productId, addonIds } = data;
  const responses: any[] = [];

  try {
    // Loop through each addonId and make a separate POST request
    for (const addonId of addonIds) {
      const response = await api.post<any>("/addons", {
        productId,
        addonId,
      });
      responses.push(response.data);
    }
    return responses;
  } catch (error) {
    console.error("Create Product Addons error:", error);
    return null;
  }
};

// Function to delete a Product addon by ID
export const deleteProductAddons = async (productAddonId: number): Promise<boolean> => {
  try {
    await api.delete(`/addons/${productAddonId}`);
    return true;
  } catch (error) {
    console.error("Delete Product error:", error);
    return false;
  }
};

// // Function to attach-modifiers-ingredients
// export const attachModifiersIngredients = async (
//   data: any
// ): Promise<any | null> => {
//   try {
//     const response = await api.post<IProduct>(
//       "/products-attachments/attach-modifiers-ingredients",
//       data
//     );
//     return response.data;
//   } catch (error) {
//     console.error("Create Product error:", error);
//     return null;
//   }
// };

// // Function to attach-modifiers-ingredients
// export const getAttachedModifiersIngredients = async (
//   ProductId: number
// ): Promise<any | null> => {
//   try {
//     const response = await api.get<IProduct>(
//       `/products-attachments/${ProductId}`
//     );
//     return response.data;
//   } catch (error) {
//     console.error("Create Product error:", error);
//     return [];
//   }
// };

// Function to assign an option to a product
export const assignOptionToProduct = async (
  productId: string,
  data: {
    optionId: string;
    maxAllowed: number;
  },
  branchId: number
): Promise<any | null> => {
  try {
    const response = await api.post<any>(`/products/${productId}/assign-option`, data);
    return response.data;
  } catch (error) {
    console.error("Assign option to product error:", error);
    return null;
  }
};

// Function to assign variations to a product
export const assignVariationsToProduct = async (
  productId: string,
  data: {
    variations: {
      id: string;
      price: number;
    }[];
  },
  branchId: number
): Promise<any | null> => {
  try {
    const response = await api.post<any>(`/products/${productId}/assign-variations`, data);
    return response.data;
  } catch (error) {
    console.error("Assign variations to product error:", error);
    return null;
  }
};

// Function to get all options of a product by productId
export const getProductOptions = async (productId: string): Promise<any | null> => {
  try {
    const response = await api.get<any>(`/products/${productId}/options`);
    return response.data;
  } catch (error) {
    console.error("Get product options error:", error);
    return null;
  }
};

// Function to get all variations of a product by productId
export const getProductVariations = async (productId: string): Promise<any | null> => {
  try {
    const response = await api.get<any>(`/products/${productId}/variations`);
    return response.data;
  } catch (error) {
    console.error("Get product variations error:", error);
    return null;
  }
};

// Function to get all variations by productId and optionId
export const getProductOptionVariations = async (productId: string, optionId: string): Promise<any | null> => {
  try {
    const response = await api.get<any>(`/products/${productId}/options/${optionId}/variations`);
    return response.data;
  } catch (error) {
    console.error("Get product option variations error:", error);
    return null;
  }
};

// Function to combine product options and their variations
export const getProductOptionsWithVariations = async (
  productId: string
): Promise<{ option: any; maxAllowed: number; variations: any[] }[] | null> => {
  try {
    // Step 1: Get all options of the product
    const options = await getProductOptions(productId);

    if (!options) {
      return [];
    }

    // Step 2: For each option, fetch its variations
    const result = await Promise.all(
      options.productOptions.map(async ({ option, maxAllowed }: any) => {
        const optionVariations = await getProductOptionVariations(productId, option.id);

        const mixedVariations = optionVariations.map((item: any) => ({
          ...item,
          name: item.variation?.name,
          description: item.variation?.description,
          imageUrl: item.variation?.imageUrl,
          availabilityStatus: item.variation?.availabilityStatus || item.availabilityStatus,
        }));

        return {
          option,
          maxAllowed: maxAllowed || 0, // assuming 'maxAllowed' is in the option
          variations: mixedVariations || [],
        };
      })
    );

    return result;
  } catch (error) {
    console.error("Get product options with variations error:", error);
    return [];
  }
};

// Function to remove variations from a product
export const removeProductVariations = async (productId: string, variations: string[]): Promise<any | null> => {
  try {
    const response = await api.delete(`/products/${productId}/remove-variations`, {
      data: {
        variations: variations,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Remove product variations error:", error);
    return null;
  }
};

// Function to remove options from a product
export const removeProductOptions = async (productId: string, options: string[]): Promise<any | null> => {
  try {
    const response = await api.delete(`/products/${productId}/remove-options`, {
      data: {
        options: options,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Remove product options error:", error);
    return null;
  }
};

// Function to update an option for a product
export const updateProductOption = async (productId: string, optionId: string, maxAllowed: number): Promise<any | null> => {
  try {
    const response = await api.patch<any>(`/products/${productId}/update-option`, {
      optionId,
      maxAllowed,
    });
    return response.data;
  } catch (error) {
    console.error("Update product option error:", error);
    return null;
  }
};

// Function to update variations for a product
export const updateProductVariations = async (
  productId: string,
  variations: {
    id: string;
    price: number;
  }[],
  branchId: number
): Promise<any | null> => {
  try {
    const response = await api.patch<any>(`/products/${productId}/update-variations`, { variations });
    return response.data;
  } catch (error) {
    console.error("Update product variations error:", error);
    return null;
  }
};
