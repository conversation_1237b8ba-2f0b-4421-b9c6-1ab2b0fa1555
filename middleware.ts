import { NextResponse } from "next/server";
import Negotiator from "negotiator";
import { getToken } from "next-auth/jwt";

const defaultLocale = "fr";
const locales = ["en", "ar", "fr"];

const protectedRoutes = ["pos", "orders", "customers", "tables", "session"];

// Get the preferred locale, similar to above or using a library
function getLocale(request: Request) {
  const acceptedLanguage = request.headers.get("accept-language") ?? undefined;
  const headers = { "accept-language": acceptedLanguage };
  const languages = new Negotiator({ headers }).languages();

  // return match(languages, locales, defaultLocale);
  return defaultLocale;
}

export async function middleware(request: any) {
  try {
    // Check if there is any supported locale in the pathname
    const pathname = request.nextUrl.pathname;

    const pathnameIsMissingLocale = locales.every((locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`);
    // Redirect if there is no locale
    if (pathnameIsMissingLocale) {
      const locale = getLocale(request);

      // e.g. incoming request is /products
      // The new URL is now /en-US/products
      return NextResponse.redirect(new URL(`/${locale}/${pathname}`, request.url));
    }

    // Check if the user is already authenticated
    const token: any = await getToken({
      req: request,
      secret: process.env.AUTH_SECRET,
    });

    // Check if the token has expired
    if (token?.exp && Date.now() >= token.exp * 1000) {
      console.log("Token expired. Logging out the user.");
      const url = new URL("/auth/logout", request.url); // Redirect to logout route
      return NextResponse.redirect(url);
    }

    // Redirect authenticated users away from /auth routes
    if (token && pathname.includes("/auth")) {
      const locale = getLocale(request);
      return NextResponse.redirect(new URL(`/${locale}/pos`, request.url));
    }

    // Check for protected routes and if the user is authenticated
    const isProtectedRoute = protectedRoutes.some((route) => pathname.includes(route));

    if (isProtectedRoute && !token) {
      const url = new URL("/auth/login", request.url);
      url.searchParams.set("callbackUrl", request.url); // Redirect back to the original URL after login
      return NextResponse.redirect(url);
    }

    return NextResponse.next();
  } catch (error) {
    console.log("hello : ", error);
  }
}

export const config = {
  matcher: [
    // Skip all internal paths (_next, assets, api)
    //"/((?!api|assets|.*\\..*|_next).*)",
    "/((?!api|assets|docs|.*\\..*|_next).*)",
    // Optional: only run on root (/) URL
  ],
};
