"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { getCategories } from "@/services/category";
import { getProductOptionsWithVariations, getProductsByCategory } from "@/services/product";
import { Icon } from "@iconify/react";
import { useEffect, useRef, useState } from "react";

import { BasketTable } from "@/components/pos/basket-table";
import { ConfirmOrderModal, ProductModal, SelectTableModal } from "@/components/pos/modals";
import { SelectCustomer } from "@/components/pos/select-customer";
import { Label } from "@/components/ui/label";
import Loading from "@/components/ui/loading";
import { getCouponByCode } from "@/services/coupon";
import { getCustomers } from "@/services/customer";
import { getTaxes } from "@/services/tax";
import toast from "react-hot-toast";

const POSPageView = ({ trans }: any) => {
  const [categories, setCategories] = useState<any[]>([]);
  const [customers, setCustomers] = useState<any[]>([]);
  const [taxes, setTaxes] = useState<any[]>([]);

  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<any>(null);
  const [products, setProducts] = useState<any[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [basketItems, setBasketItems] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [isConfimModalOpen, setIsConfirmModalOpen] = useState(false);
  const [selectTableOpen, setSelectTableOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(1);
  const [selectedTable, setSelectedTable] = useState<any>(null);
  const [couponCode, setCouponCode] = useState("");
  const [validCoupon, setValidCoupon] = useState<any>(null);
  const [discountAmount, setDiscountAmount] = useState(0);
  const [showLeftButton, setShowLeftButton] = useState(false);
  const [showRightButton, setShowRightButton] = useState(true);

  useEffect(() => {
    const fetchCategories = async () => {
      setLoading(true);
      const categoriesData = await getCategories();
      const customersData = await getCustomers();
      const taxesData = await getTaxes();

      setTaxes(taxesData || []);
      setCustomers(customersData || []);
      setCategories(categoriesData || []);
      setSelectedCategoryId(categoriesData?.[0]?.id);

      setLoading(false);
    };
    fetchCategories();
  }, []);

  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      if (selectedCategoryId) {
        const productsData = await getProductsByCategory(selectedCategoryId);
        setProducts(productsData || []);
      }

      setLoading(false);
    };
    fetchProducts();
  }, [selectedCategoryId]);

  useEffect(() => {
    checkScrollPosition();
    window.addEventListener("resize", checkScrollPosition);
    return () => window.removeEventListener("resize", checkScrollPosition);
  }, [categories]);

  const addToBasket = (product: any, quantity: number, selectedVariations: any, amount: string, specialInstructions: string = "") => {
    setBasketItems((prevItems) => [...prevItems, { product, qty: quantity, variations: selectedVariations, amount, specialInstructions }]);
  };

  const calculateTotal = () => {
    let subtotal = basketItems.reduce((total, item) => total + parseFloat(item.amount), 0);
    const safeDiscount = Math.min(discountAmount, subtotal);
    const tva = calculateTax();
    subtotal += tva;
    return subtotal - safeDiscount;
  };

  const calculateTax = () => {
    const subtotal = basketItems.reduce((total, item) => total + parseFloat(item.amount), 0);
    return (subtotal * (taxes[0]?.taxRate || 0)) / 100;
  };

  const validateCouponCode = async () => {
    if (!couponCode) {
      toast.error("Veuillez entrer un code de coupon", { position: "bottom-center" });
      return;
    }

    try {
      const coupon = await getCouponByCode(couponCode);

      if (!coupon) {
        toast.error("Code de coupon invalide", { position: "bottom-center" });
        setValidCoupon(null);
        setDiscountAmount(0);
        return;
      }

      let discount = 0;
      const subtotal = basketItems.reduce((total, item) => total + parseFloat(item.amount), 0);

      if (coupon.couponType === "PERCENTAGE") {
        discount = (subtotal * coupon.discountValue) / 100;
        if (coupon.maxDiscountAmount && discount > coupon.maxDiscountAmount) {
          discount = coupon.maxDiscountAmount;
        }
      } else {
        discount = Math.min(coupon.discountValue, subtotal);
      }

      setValidCoupon(coupon);
      setDiscountAmount(discount);
      toast.success(`Coupon valide - ${coupon.description}`, { position: "bottom-center" });
    } catch (error) {
      console.error("Error validating coupon:", error);
      toast.error("Erreur lors de la validation du coupon", { position: "bottom-center" });
      setValidCoupon(null);
      setDiscountAmount(0);
    }
  };

  const categoriesRef = useRef(null);

  const scrollCategories = (scrollOffset: any) => {
    if (categoriesRef.current) {
      (categoriesRef.current as HTMLDivElement).scrollBy({
        left: scrollOffset,
        behavior: "smooth",
      });
    }

    setTimeout(checkScrollPosition, 300);
  };

  const checkScrollPosition = () => {
    if (categoriesRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = categoriesRef.current;
      const isAtStart = scrollLeft <= 0;
      const isAtEnd = scrollLeft >= scrollWidth - clientWidth - 1;

      setShowLeftButton(!isAtStart);
      setShowRightButton(!isAtEnd);
    }
  };

  const handleCategorySelect = (id: any) => {
    setSelectedCategoryId(id);
  };

  const handleProductClick = async (product: any) => {
    const optionsAndVariations = await getProductOptionsWithVariations(product.id as any);

    console.log("Options with Variations:", optionsAndVariations);

    if (product.isAddon) {
      setBasketItems((prevItems) => {
        const existingItemIndex = prevItems.findIndex((item) => item.product.id === product.id);

        if (existingItemIndex !== -1) {
          return prevItems.map((item, index) =>
            index === existingItemIndex
              ? {
                  ...item,
                  qty: item.qty + 1,
                  amount: (
                    (item.qty + 1) *
                    (product.price + item.variations.reduce((total: number, variation: any) => total + variation.price, 0))
                  ).toFixed(2),
                }
              : item
          );
        }

        return [...prevItems, { product, qty: 1, variations: [], amount: product.price.toFixed(2) }];
      });
      toast.success("Addon produit ajouté au panier", { position: "bottom-center" });
      return;
    }

    setSelectedProduct({
      product,
      optionsAndVariations: optionsAndVariations || [],
    });
  };

  const closeModal = () => {
    setSelectedProduct(null);
  };

  const handleIncrement = (index: number) => {
    setBasketItems((prevItems) =>
      prevItems.map((item, i) =>
        i === index
          ? {
              ...item,
              qty: item.qty + 1,
              amount: (
                (item.qty + 1) *
                (item.product.price + item.variations.reduce((total: number, variation: any) => total + variation.price, 0))
              ).toFixed(2),
            }
          : item
      )
    );
  };

  const handleDecrement = (index: number) => {
    setBasketItems((prevItems) =>
      prevItems
        .map((item, i) =>
          i === index && item.qty > 1
            ? {
                ...item,
                qty: item.qty - 1,
                amount: (
                  (item.qty - 1) *
                  (item.product.price + item.variations.reduce((total: number, variation: any) => total + variation.price, 0))
                ).toFixed(2),
              }
            : item
        )
        .filter((item) => item.qty > 0)
    );
  };

  const handleDelete = (index: number) => {
    setBasketItems((prevItems) => prevItems.filter((_, i) => i !== index));
    toast.success("Produit retiré du panier.", { position: "bottom-center" });
  };

  const clearBasket = () => {
    setBasketItems([]);
    toast.success("Panier vidé.", { position: "bottom-center" });
  };

  return (
    <>
      <div className="grid grid-cols-4 gap-4 min-h-[100vh]  p-4">
        {/* Categories and Products Card */}

        <div className="flex-col col-span-3 w-full h-full">
          <div className="relative px-2">
            {/* Left navigation button */}
            {showLeftButton && (
              <button
                onClick={() => scrollCategories(-300)}
                className="absolute left-0 top-1/2 rounded-l-xl -translate-y-1/2 z-10 bg-primary-100 dark:bg-neutral-800 opacity-80 hover:opacity-100 w-10 h-full flex items-center justify-center shadow-md dark:hover:bg-neutral-700 transition-opacity duration-200"
              >
                <Icon icon="heroicons:chevron-left" className="w-5 h-5" />
              </button>
            )}

            {/* Categories scroll container */}
            <div
              ref={categoriesRef}
              onScroll={checkScrollPosition}
              className="flex items-center space-x-4 py-2 overflow-x-auto [&::-webkit-scrollbar]:h-[0px]
                [&::-webkit-scrollbar-track]:rounded-full
                [&::-webkit-scrollbar-track]:bg-gray-100
                [&::-webkit-scrollbar-thumb]:rounded-full
                [&::-webkit-scrollbar-thumb]:bg-primary-300
                dark:[&::-webkit-scrollbar-track]:bg-neutral-700
                dark:[&::-webkit-scrollbar-thumb]:bg-neutral-50 w-full scroll-smooth"
            >
              {categories.map((category) => (
                <div
                  key={category.id}
                  onClick={() => handleCategorySelect(category.id)}
                  className={cn(
                    "min-w-[100px] h-full p-2 cursor-pointer rounded-xl bg-white dark:bg-neutral-800 shadow-sm hover:bg-primary-50 hover:border-b-2 hover:border-b-primary-500",
                    {
                      "bg-primary-50 border-b-2 border-b-primary-500": selectedCategoryId === category.id,
                    }
                  )}
                >
                  <img src={category.imageUrl} alt={category.name} className="w-10 h-10 object-cover rounded mx-auto" />
                  <p className="mt-4 text-center font-medium">{category.name}</p>
                </div>
              ))}
            </div>

            {/* Right navigation button */}
            {showRightButton && (
              <button
                onClick={() => scrollCategories(300)}
                className="absolute right-0 top-1/2 rounded-r-xl -translate-y-1/2 z-10 bg-primary-100 dark:bg-neutral-800 opacity-80 hover:opacity-100 w-10 h-full flex items-center justify-center shadow-md dark:hover:bg-neutral-700 transition-opacity duration-200"
              >
                <Icon icon="heroicons:chevron-right" className="w-5 h-5" />
              </button>
            )}
          </div>

          <div
            className="flex-grow h-[80vh] p-2 overflow-y-auto [&::-webkit-scrollbar]:w-[5px]
            [&::-webkit-scrollbar-track]:rounded-full
            [&::-webkit-scrollbar-track]:bg-gray-100
            [&::-webkit-scrollbar-thumb]:rounded-full
            [&::-webkit-scrollbar-thumb]:bg-primary-300
            dark:[&::-webkit-scrollbar-track]:bg-neutral-700
            dark:[&::-webkit-scrollbar-thumb]:bg-neutral-50"
          >
            {/* Loading state - show spinner while loading */}
            {loading && (
              <div className="h-full flex justify-center items-center">
                <div className="text-center flex flex-col items-center">
                  <Loading />
                </div>
              </div>
            )}

            {/* Content state - show when not loading */}
            {!loading && (
              <>
                {/* Empty state - when no products */}
                {products.length === 0 && (
                  <div className="h-full flex justify-center items-center">
                    <div className="text-center flex flex-col items-center">
                      <Icon icon="heroicons:exclamation-circle" className="text-7xl text-default-300" />
                      <div className="mt-4 text-lg font-medium text-default-500">Aucun produit trouvé pour cette catégorie</div>
                    </div>
                  </div>
                )}

                {/* Product grid - when products exist */}
                {products.length > 0 && (
                  <div className="grid sm:grid-cols-2 md:grid-cols-5 lg:grid-cols-5 gap-4 mt-4">
                    {products.map((product) => (
                      <div
                        onClick={() => handleProductClick(product)}
                        key={product.id}
                        className="bg-white dark:bg-neutral-800 shadow-sm p-2 rounded-xl cursor-pointer
                        hover:shadow-md transition-shadow duration-200"
                      >
                        <img
                          src={product.imageUrl || "https://adstandards.com.au/wp-content/uploads/2023/08/food_and_beverage.svg"}
                          alt={product.name}
                          className="w-32 h-32 object-cover rounded mx-auto p-0"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = "https://adstandards.com.au/wp-content/uploads/2023/08/food_and_beverage.svg";
                          }}
                        />

                        <div className="flex justify-between items-center mt-2">
                          <p className="text-md font-medium truncate">{product.name}</p>
                          <p className="font-bold whitespace-nowrap">{product.price} DH</p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>

          {selectedProduct && (
            <ProductModal
              setLoading={setLoading}
              isOpen={!!selectedProduct}
              onClose={closeModal}
              productData={selectedProduct}
              addToBasket={addToBasket}
            />
          )}
        </div>

        {/* Billing Section */}
        <Card className="p-4 shadow-sm">
          <div className="mb-2">
            <div className="flex items-center gap-3">
              <SelectCustomer setSelectedCustomer={setSelectedCustomer} selectedCustomer={selectedCustomer} customers={customers} />
            </div>
          </div>
          <div className="flex flex-col text-sm h-[calc(100%-50px)]">
            <div
              className="max-h-[460px] overflow-x-auto [&::-webkit-scrollbar]:w-[5px]
                      [&::-webkit-scrollbar-track]:rounded-full
                      [&::-webkit-scrollbar-track]:bg-gray-100
                      [&::-webkit-scrollbar-thumb]:rounded-full
                      [&::-webkit-scrollbar-thumb]:bg-primary-300
                      dark:[&::-webkit-scrollbar-track]:bg-neutral-700
                      dark:[&::-webkit-scrollbar-thumb]:bg-neutral-50  pb-3 w-full"
            >
              {basketItems.length > 0 ? (
                <BasketTable basketTableItems={basketItems} onIncrement={handleIncrement} onDecrement={handleDecrement} onDelete={handleDelete} />
              ) : (
                <div className="flex flex-col items-center justify-center text-center w-full h-full text-gray-500 pt-8">
                  <Icon icon="heroicons:shopping-cart" className="text-5xl text-default-300" />
                  <p className="text-lg ">Aucun article dans le panier</p>
                </div>
              )}
            </div>

            <div className="flex space-x-4 mb-4 mt-auto">
              <SelectTableModal
                setLoading={setLoading}
                isOpen={selectTableOpen}
                onClose={() => setSelectTableOpen(false)}
                selectedTable={selectedTable}
                setSelectedTable={setSelectedTable}
              />
              <div className="space-x-2 flex w-full">
                <Card
                  className={`w-full cursor-pointer shadow-none border-[2px] ${
                    selectedOption === 1 ? "border-orange-500 bg-primary-50" : "border-gray-200"
                  }`}
                  onClick={() => {
                    setSelectedOption(1);
                    setSelectedTable(null);
                  }}
                >
                  <CardContent className="flex items-center h-full p-2">
                    <Label className="flex items-center cursor-pointer w-full">
                      <Icon icon="heroicons:shopping-bag" className="h-5 w-5 mr-2" />
                      <div className="font-medium">À emporter</div>
                    </Label>
                  </CardContent>
                </Card>

                <Card
                  className={`w-full cursor-pointer shadow-none border-[2px] ${
                    selectedOption === 2 ? "border-orange-500 bg-primary-50" : "border-gray-200"
                  }`}
                  onClick={() => {
                    setSelectedOption(2);
                    setSelectTableOpen(true);
                  }}
                >
                  <CardContent className="text-center h-full p-2">
                    <Label className="flex items-center cursor-pointer w-full">
                      <Icon icon="material-symbols:table-restaurant-outline-rounded" className="h-6 w-6 mr-2" />
                      <div className="font-medium">Sur place</div>
                    </Label>
                    {selectedTable && <div className="text-xs text-gray-500">({selectedTable.name})</div>}
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Coupon Code Section */}
            <div className="flex items-center justify-between mb-4 gap-x-2">
              <input
                type="text"
                placeholder="Entrez un code promo"
                className="border rounded-md px-3 py-2 w-full"
                value={couponCode}
                onChange={(e) => {
                  setCouponCode(e.target.value);
                  if (validCoupon) {
                    setValidCoupon(null);
                    setDiscountAmount(0);
                  }
                }}
              />
              <button
                onClick={validateCouponCode}
                className="bg-primary-500 text-white px-4 py-2 rounded-md hover:bg-primary-600 transition"
                disabled={!couponCode}
              >
                Valider
              </button>
            </div>

            {/* Total Summary */}
            <div>
              <div className="flex mb-2 justify-between">
                <span>Total HT:</span>
                <span className="font-semibold">{basketItems.reduce((total, item) => total + parseFloat(item.amount), 0).toFixed(2)} DH</span>
              </div>
              {taxes.length > 0 && taxes[0].taxRate > 0 ? (
                <div className="flex mb-2 justify-between">
                  <span>TVA ({taxes[0].taxRate}%):</span>
                  <span>{calculateTax().toFixed(2)} DH</span>
                </div>
              ) : null}
              {validCoupon && (
                <div className="flex mb-2 justify-between">
                  <span>Remise Coupon ({validCoupon.code}):</span>
                  <span className="text-green-600">-{discountAmount.toFixed(2)} DH</span>
                </div>
              )}

              <div className="flex mb-2 justify-between text-lg font-bold">
                <span>Total TTC:</span>
                <span>{calculateTotal().toFixed(2)} DH</span>
              </div>
            </div>

            <div className="flex items-center space-x-2 ">
              <ConfirmOrderModal
                setLoading={setLoading}
                isOpen={isConfimModalOpen}
                onClose={() => setIsConfirmModalOpen(false)}
                setBasketItems={setBasketItems}
                basketItems={basketItems}
                selectedTable={selectedTable}
                setSelectedTable={setSelectedTable}
                totalToPay={calculateTotal().toFixed(2)}
                taxes={taxes}
                calculateTax={calculateTax().toFixed(2)}
                selectedCustomer={selectedCustomer}
                setSelectedCustomer={setSelectedCustomer}
                customers={customers}
                selectedType={selectedOption}
                setSelectedType={setSelectedOption}
                validCoupon={validCoupon}
                discountAmount={discountAmount}
              />

              <Button disabled={basketItems.length === 0} className="w-full" color="destructive" onClick={clearBasket}>
                Annuler
              </Button>
              <Button disabled={basketItems.length === 0} className="w-full" color="success" onClick={() => setIsConfirmModalOpen(true)}>
                Commander
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </>
  );
};

export default POSPageView;
