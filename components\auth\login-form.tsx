"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { signIn } from "next-auth/react";
import Link from "next/link";
import React from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { z } from "zod";

import { Checkbox } from "@/components/ui/checkbox";
import { Icon } from "@iconify/react";

import { useMediaQuery } from "@/hooks/use-media-query";
import { useRouter } from "next/navigation";

const schema = z.object({
  email: z.string().min(4),
  password: z.string().min(4),
});

const LogInForm = ({ trans }: any) => {
  const [passwordType, setPasswordType] = React.useState("password");
  const router = useRouter();
  const togglePasswordType = () => {
    if (passwordType === "text") {
      setPasswordType("password");
    } else if (passwordType === "password") {
      setPasswordType("text");
    }
  };
  const {
    register,

    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    resolver: zodResolver(schema),
    mode: "all",
    defaultValues: {
      email: "<EMAIL>",
      password: "admin123",
    },
  });

  const [isVisible, setIsVisible] = React.useState(false);

  const toggleVisibility = () => setIsVisible(!isVisible);
  const isDesktop2xl = useMediaQuery("(max-width: 1530px)");

  const onSubmit = async (data: any) => {
    try {
      let response = await signIn("credentials", {
        email: data.email,
        password: data.password,
        redirect: false,
      });
      if (response && response.ok) {
        toast.success("Login Successful", { position: "bottom-center" });
        router.push("/session");
      } else {
        toast.error("Incorrect email and password", { position: "bottom-center" });
      }
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <div className="w-full h-full flex flex-col justify-center">
      <form onSubmit={handleSubmit(onSubmit)} className="2xl:mt-7 mt-8">
        <div className="relative">
          <Input
            removeWrapper
            type="text"
            id="email"
            size={!isDesktop2xl ? "xl" : "lg"}
            placeholder=" "
            disabled={isSubmitting}
            {...register("email")}
            className={cn("peer", {
              "border-destructive": errors.email,
            })}
          />
          <Label
            htmlFor="email"
            className={cn(
              " absolute text-base text-default-600  rounded-t duration-300 transform -translate-y-5 scale-75 top-2 z-10 origin-[0]   bg-background  px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75  peer-focus:-translate-y-4 rtl:peer-focus:translate-x-1/4 rtl:peer-focus:left-auto start-1",
              {
                " text-sm ": isDesktop2xl,
              }
            )}
          >
            Nom d'utilisateur
          </Label>
        </div>
        {errors.email && <div className=" text-destructive mt-2">{errors.email.message}</div>}

        <div className="relative mt-6">
          <Input
            removeWrapper
            type={passwordType === "password" ? "password" : "text"}
            id="password"
            size={!isDesktop2xl ? "xl" : "lg"}
            placeholder=" "
            disabled={isSubmitting}
            {...register("password")}
            className={cn("peer", {
              "border-destructive": errors.password,
            })}
          />
          <Label
            htmlFor="password"
            className={cn(
              " absolute text-base  rounded-t text-default-600  duration-300 transform -translate-y-5 scale-75 top-2 z-10 origin-[0]   bg-background  px-2 peer-focus:px-2 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75  peer-focus:-translate-y-4 rtl:peer-focus:translate-x-1/4 rtl:peer-focus:left-auto start-1",
              {
                " text-sm ": isDesktop2xl,
              }
            )}
          >
            Mot de passe
          </Label>
          <div className="absolute top-1/2 -translate-y-1/2 ltr:right-4 rtl:left-4 cursor-pointer" onClick={togglePasswordType}>
            {passwordType === "password" ? (
              <Icon icon="heroicons:eye" className="w-4 h-4 text-default-400" />
            ) : (
              <Icon icon="heroicons:eye-slash" className="w-4 h-4 text-default-400" />
            )}
          </div>
        </div>
        {errors.password && <div className=" text-destructive mt-2">{errors.password.message}</div>}

        <div className="mt-5  mb-6 flex flex-wrap gap-2">
          <div className="flex-1 flex  items-center gap-1.5 ">
            <Checkbox size="sm" className="border-default-300 mt-[1px]" id="isRemebered" />
            <Label htmlFor="isRemebered" className="text-sm text-default-600 cursor-pointer whitespace-nowrap">
              Se souvenir de moi
            </Label>
          </div>
          <Link href="/auth/forgot" className="flex-none text-sm text-primary" aria-disabled>
            Mot de passe oublié ?
          </Link>
        </div>
        <Button className="w-full" disabled={isSubmitting} size={!isDesktop2xl ? "lg" : "md"}>
          {isSubmitting && <Loader2 className="ltr:mr-2 rtl:ml-2 h-4 w-4 animate-spin" />}
          {isSubmitting ? "Chargement..." : "Se connecter"}
        </Button>
      </form>

      {/* <div className="mt-6 text-center text-base text-default-600">
        Don't have an account?{" "}
        <Link href="/auth/register" className="text-primary">
          Sign Up{" "}
        </Link>
      </div> */}
    </div>
  );
};

export default LogInForm;
